# ==================================================
# 主配置文件：nginx.conf
# ==================================================
user  nginx;
worker_processes  2;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
    multi_accept        on;
    use                 epoll;
}

http {
    # ==================================================
    # 通用HTTP配置（原common.conf内容）
    # ==================================================
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    # 日志格式
    log_format main  '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    log_format upstream_log '[$time_local] | $host | $remote_addr | $request | '
                           '$request_time | $body_bytes_sent | $status | '
                           '$upstream_addr | $upstream_response_time | $upstream_status';

    access_log  /var/log/nginx/access.log  main;

    # 传输优化
    sendfile        on;
    tcp_nopush      on;
    keepalive_timeout  65;

    # 安全参数
    client_max_body_size    50m;
    client_header_timeout   30;
    client_body_timeout     30;
    send_timeout            60;
    reset_timedout_connection on;

    # 压缩配置
    gzip  on;
    gzip_types text/plain text/css application/json application/javascript
              text/xml application/xml application/xml+rss text/javascript;

    # ==================================================
    # 上游服务配置（原upstream.conf内容）
    # ==================================================
    upstream backend {
        # 负载均衡配置
        server domino-admin-qh:8200 weight=2 max_fails=1 fail_timeout=6;

        # 负载均衡算法
        least_conn;
        keepalive 32;  # 长连接优化
    }

    # ==================================================
    # 虚拟主机配置（原server.conf内容）
    # ==================================================
    server {
        listen 80;
        server_name **************;

        # 前端静态资源
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            index index.html;

            # 静态资源缓存
            location ~* \.(js|css|png|jpg|jpeg|gif|ico)$ {
                expires 30d;
                add_header Cache-Control "public, no-transform";
            }
        }

        # API代理配置
        location /prod-api/ {
            proxy_pass http://backend/domino/;

            # 请求头标准化
            proxy_set_header Host               $host;
            proxy_set_header X-Real-IP          $remote_addr;
            proxy_set_header X-Forwarded-For    $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto  $scheme;

            # 超时参数
            proxy_connect_timeout   5s;
            proxy_send_timeout      30s;
            proxy_read_timeout      900s;

            # 缓冲区优化
            proxy_buffering         on;
            proxy_buffer_size       4k;
            proxy_buffers           4 32k;
            proxy_busy_buffers_size 64k;

            # Cookie路径修正
            proxy_cookie_path ~^/domino/(.*) /prod-api/$1;

            # 跨域配置
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range' always;
        }

        # 错误页配置
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
}
