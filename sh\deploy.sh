#!/usr/bin/env bash
echo "===== 开始前端部署 ====="
set -euo pipefail

# ==================== 参数定义 ====================
mirrors_url="${1:?镜像仓库地址未指定}"
mirrors_namespace="${2:?命名空间未指定}"
mirrors_project_name="${3:?前端项目名称未指定}"
tag="${4:?镜像标签未指定}"
host_port="${5:?宿主机端口未指定}"
container_port="80"  # 固定容器内部端口
network="${6:-domino-qh}"
nginx_conf_path="${7:?Nginx配置目录未指定}"  # 改为Nginx配置路径
username="${8:?用户名未指定}"
password="${9:?密码未指定}"

image_name="${mirrors_url}/${mirrors_namespace}/${mirrors_project_name}:${tag}"

# ==================== 日志配置 ====================
LOG_FILE="frontend-deploy.log"
exec > >(tee -a "$LOG_FILE") 2>&1

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*"
}

# ==================== 函数定义 ====================
cleanup() {
    log "执行清理操作..."
    docker logout "$mirrors_url" || true
}

stop_frontend_container() {
    log "正在停止前端容器 [${mirrors_project_name}]..."
    local container_ids=$(docker ps -aq --filter "name=^${mirrors_project_name}$")

    if [[ -n "$container_ids" ]]; then
        for container_id in $container_ids; do
            log "强制停止容器 ${mirrors_project_name} (ID: ${container_id})"
            docker rm -f "$container_id" || log "容器删除失败，可能不存在"
        done
    fi
}

force_release_port() {
    local target_port=$1

    log "===== 开始清理端口 ${target_port} 占用 ====="

    # 清理Docker容器占用
    local docker_conflicts=$(docker ps --format "{{.ID}}\t{{.Names}}" | grep ":${target_port}->" | awk '{print $1}')
    if [[ -n "$docker_conflicts" ]]; then
        log "发现Docker容器占用端口："
        docker ps --filter "id=${docker_conflicts}"
        log "强制删除占用容器..."
        docker rm -f $docker_conflicts
    fi

    # 清理宿主机进程占用
    local pids=$(ss -tulnp | grep ":${target_port} " | awk '{print $NF}' | awk -F'pid=' '{print $2}' | awk -F',' '{print $1}' | sort -u)
    if [[ -n "$pids" ]]; then
        log "发现宿主机进程占用："
        ps -p $pids
        log "正在终止进程..."
        kill -9 $pids
        sleep 1
        log "验证进程状态："
        ps -p $pids &>/dev/null && log "错误：仍有进程存活" || log "端口已释放"
    fi

    log "===== 端口清理完成 ====="
}

# ==================== 主流程 ====================
trap cleanup EXIT

log "======== 前端部署参数详情 ========"
log "镜像仓库地址: $mirrors_url"
log "命名空间: $mirrors_namespace"
log "前端项目名称: $mirrors_project_name"
log "镜像标签: $tag"
log "宿主机端口: $host_port"
log "容器端口: $container_port"
log "Nginx配置路径: $nginx_conf_path"
log "用户名: $username"
log "密码: ******"
log "镜像名称: $image_name"
log "容器网络: $network"

log "======== 开始部署前端 ${mirrors_project_name}:${tag} ========"

# 参数验证
if ! [[ "$host_port" =~ ^[0-9]+$ ]]; then
    log "错误：宿主机端口号必须为数字"
    exit 1
fi

# 检查Nginx配置目录
if [[ ! -d "$nginx_conf_path" ]]; then
    log "错误：Nginx配置目录不存在 $nginx_conf_path"
    exit 1
fi

# Docker 网络检查与创建
log "检查 Docker 网络 [$network]..."
if ! docker network inspect "$network" &>/dev/null; then
    log "创建网络 $network..."
    docker network create "$network"
fi

# Docker 登录
log "正在登录镜像仓库 ${mirrors_url}..."
echo "$password" | docker login -u "$username" --password-stdin "$mirrors_url"

# 拉取新镜像
log "正在拉取前端镜像 ${image_name}..."
docker pull "$image_name"

# 停止旧容器并释放端口
log "停止旧前端容器..."
stop_frontend_container
log "强制释放宿主机端口..."
force_release_port "$host_port"

# 启动新容器
log "正在启动前端容器..."
docker run \
    --network "$network" \
    --name "$mirrors_project_name" \
    -d \
    -p "${host_port}:${container_port}" \
    -v "${nginx_conf_path}:/etc/nginx/conf.d" \
    -v "${nginx_conf_path}/../logs:/var/log/nginx" \
    "$image_name"

# 清理旧镜像
log "清理历史前端镜像..."
current_image_id=$(docker inspect --type=image -f '{{.Id}}' "$image_name" 2>/dev/null || true)
if [[ -z "$current_image_id" ]]; then
    log "错误：无法获取当前镜像ID"
    exit 1
fi

# 查找并删除同名仓库但不同标签的旧镜像
docker images --format "{{.Repository}}:{{.Tag}} {{.ID}}" | grep "^${mirrors_url}/${mirrors_namespace}/${mirrors_project_name}:" | while read -r image_tag image_id; do
    if [[ "$image_tag" != "$image_name" && "$image_id" != "$current_image_id" ]]; then
        log "删除旧前端镜像 $image_tag ($image_id)"
        docker rmi -f "$image_id" || log "删除镜像失败，可能已被其他容器引用"
    fi
done

log "======== 前端部署成功 ========"
log "容器名称: ${mirrors_project_name}:${tag}"
log "访问地址: http://<服务器IP>:${host_port}"
