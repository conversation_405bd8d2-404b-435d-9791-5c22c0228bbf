import request from '@/utils/request'

// 查询知识点结构列表
export function selectKnowledgeTreePage(query) {
  return request({
    url: '/qh/knowledgeTree/page',
    method: 'get',
    params: query
  })
}

// 首页数据查询
export function dashboard(query) {
  return request({
    url: '/qh/knowledgeTree/dashboard',
    method: 'get',
    params: query
  })
}

// 查询知识点结构列表
export function selectKnowledgeTreeList(query) {
  return request({
    url: '/qh/knowledgeTree/list',
    method: 'get',
    params: query
  })
}

// 查询内容下拉树结构
export function knowledgeTree(query) {
  return request({
    url: '/qh/knowledgeTree/tree',
    method: 'get',
    params: query
  })
}

// 查询知识点详细信息
export function getKnowledgeTree(id) {
  return request({
    url: '/qh/knowledgeTree/' + id,
    method: 'get'
  })
}

// 新增知识点
export function addKnowledgeTree(data) {
  return request({
    url: '/qh/knowledgeTree',
    method: 'post',
    data: data
  })
}

// 修改知识点
export function updateKnowledgeTree(data) {
  return request({
    url: '/qh/knowledgeTree',
    method: 'put',
    data: data
  })
}

// 删除知识点
export function delKnowledgeTree(id) {
  return request({
    url: '/qh/knowledgeTree/' + id,
    method: 'delete'
  })
}

// 导出知识点结构
export function exportKnowledgeTree(query) {
  return request({
    url: '/qh/knowledgeTree/export',
    method: 'get',
    params: query
  })
}

// 根据nodeType查询详细信息
export function getKnowledgeTreeByNodeType(id) {
  return request({
    url: '/qh/knowledgeTree/nodeType/' + id,
    method: 'get'
  })
}
