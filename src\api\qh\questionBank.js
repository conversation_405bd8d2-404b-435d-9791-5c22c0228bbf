import request from '@/utils/request'
import {parseStrEmpty} from "@/utils/domino";

// 查询试题列表
export function listQuestionBank(query) {
    return request({
        url: '/qh/questionBank/list',
        method: 'get',
        params: query
    })
}

// 首页数据查询
export function dashboard(query) {
    return request({
        url: '/qh/questionBank/dashboard',
        method: 'get',
        params: query
    })
}

// 查询试题详细
export function getQuestionBank(bankId) {
    return request({
        url: '/qh/questionBank/' + parseStrEmpty(bankId),
        method: 'get'
    })
}

// 新增试题
export function addQuestionBank(data) {
    return request({
        url: '/qh/questionBank',
        method: 'post',
        data: data
    })
}

// 修改试题
export function updateQuestionBank(data) {
    return request({
        url: '/qh/questionBank',
        method: 'put',
        data: data
    })
}

// 删除试题
export function delQuestionBank(bankId) {
    return request({
        url: '/qh/questionBank/' + bankId,
        method: 'delete'
    })
}

// 添加试题到试题栏
export function addQuestionBankBoard(type, bankId) {
    return request({
        url: '/qh/questionBank/board/' + type + '/' + bankId,
        method: 'put'
    })
}

// 从试题蓝中删除试题
export function delQuestionBankBoard(type, bankId) {
    return request({
        url: '/qh/questionBank/board/' + type + '/' + bankId,
        method: 'delete'
    })
}

// 查询试题栏
export function listQuestionBankBoard(type) {
    return request({
        url: '/qh/questionBank/board/' + type,
        method: 'get'
    })
}

// 更新试题栏顺序
export function updateQuestionBankBoardOrder(type, data) {
    return request({
        url: '/qh/questionBank/board/order/' + type,
        method: 'post',
        data: data
    })
}
