<template>
  <div class="app-container">
    <el-row>
      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <Cpu style="width: 1em; height: 1em; vertical-align: middle;"/>
            <span style="vertical-align: middle;">CPU</span></template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;">
              <thead>
              <tr>
                <th class="el-table__cell is-leaf">
                  <div class="cell">属性</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">值</div>
                </th>
              </tr>
              </thead>
              <tbody>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">核心数</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.cpu" class="cell">{{ server.cpu.cpuNum }}</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">用户使用率</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.cpu" class="cell">{{ server.cpu.used }}%</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">系统使用率</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.cpu" class="cell">{{ server.cpu.sys }}%</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">当前空闲率</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.cpu" class="cell">{{ server.cpu.free }}%</div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="12" class="card-box">
        <el-card>
          <template #header>
            <Tickets style="width: 1em; height: 1em; vertical-align: middle;"/>
            <span style="vertical-align: middle;">内存</span></template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;">
              <thead>
              <tr>
                <th class="el-table__cell is-leaf">
                  <div class="cell">属性</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">内存</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">JVM</div>
                </th>
              </tr>
              </thead>
              <tbody>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">总内存</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.mem" class="cell">{{ server.mem.total }}G</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.total }}M</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">已用内存</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.mem" class="cell">{{ server.mem.used }}G</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.used }}M</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">剩余内存</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.mem" class="cell">{{ server.mem.free }}G</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.free }}M</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">使用率</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.mem" :class="{'text-danger': server.mem.usage > 80}" class="cell">
                    {{ server.mem.usage }}%
                  </div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" :class="{'text-danger': server.jvm.usage > 80}" class="cell">
                    {{ server.jvm.usage }}%
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <Monitor style="width: 1em; height: 1em; vertical-align: middle;"/>
            <span style="vertical-align: middle;">服务器信息</span></template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;">
              <tbody>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">服务器名称</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.sys" class="cell">{{ server.sys.computerName }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">操作系统</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.sys" class="cell">{{ server.sys.osName }}</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">服务器IP</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.sys" class="cell">{{ server.sys.computerIp }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">系统架构</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.sys" class="cell">{{ server.sys.osArch }}</div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <CoffeeCup style="width: 1em; height: 1em; vertical-align: middle;"/>
            <span style="vertical-align: middle;">Java虚拟机信息</span></template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">Java名称</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.name }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">Java版本</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.version }}</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf">
                  <div class="cell">启动时间</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.startTime }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">运行时长</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.runTime }}</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf" colspan="1">
                  <div class="cell">安装路径</div>
                </td>
                <td class="el-table__cell is-leaf" colspan="3">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.home }}</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf" colspan="1">
                  <div class="cell">项目路径</div>
                </td>
                <td class="el-table__cell is-leaf" colspan="3">
                  <div v-if="server.sys" class="cell">{{ server.sys.userDir }}</div>
                </td>
              </tr>
              <tr>
                <td class="el-table__cell is-leaf" colspan="1">
                  <div class="cell">运行参数</div>
                </td>
                <td class="el-table__cell is-leaf" colspan="3">
                  <div v-if="server.jvm" class="cell">{{ server.jvm.inputArgs }}</div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <template #header>
            <MessageBox style="width: 1em; height: 1em; vertical-align: middle;"/>
            <span style="vertical-align: middle;">磁盘状态</span></template>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;">
              <thead>
              <tr>
                <th class="el-table__cell el-table__cell is-leaf">
                  <div class="cell">盘符路径</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">文件系统</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">盘符类型</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">总大小</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">可用大小</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">已用大小</div>
                </th>
                <th class="el-table__cell is-leaf">
                  <div class="cell">已用百分比</div>
                </th>
              </tr>
              </thead>
              <tbody v-if="server.sysFiles">
              <tr v-for="(sysFile, index) in server.sysFiles" :key="index">
                <td class="el-table__cell is-leaf">
                  <div class="cell">{{ sysFile.dirName }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">{{ sysFile.sysTypeName }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">{{ sysFile.typeName }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">{{ sysFile.total }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">{{ sysFile.free }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div class="cell">{{ sysFile.used }}</div>
                </td>
                <td class="el-table__cell is-leaf">
                  <div :class="{'text-danger': sysFile.usage > 80}" class="cell">{{ sysFile.usage }}%</div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {getServer} from '@/api/monitor/server'

const server = ref([]);
const {proxy} = getCurrentInstance();

function getList() {
  proxy.$modal.loading("正在加载服务监控数据，请稍候！");
  getServer().then(response => {
    server.value = response.data;
    proxy.$modal.closeLoading();
  });
}

getList();
</script>
