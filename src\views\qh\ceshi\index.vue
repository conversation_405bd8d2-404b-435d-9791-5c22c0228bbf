<template>
    <div class="app-container">
        <div class="basic-info-section">
            <el-form ref="paperFormRef" :model="paperForm" :rules="rules" label-width="100px">
                <el-form-item label="题库" prop="libraryIds">
                    <el-select v-model="paperForm.libraryIds" class="form-item-width" multiple placeholder="请选择题库">
                        <el-option v-for="item in libraryOptions" :key="item.value" :label="item.label" :value="item.value"/>
                    </el-select>
                </el-form-item>
                <el-form-item label="试卷名称" prop="paperName">
                    <el-input v-model="paperForm.paperName" class="form-item-width" placeholder="请输入试卷名称"/>
                </el-form-item>
            </el-form>
        </div>

        <!-- 题型数量区域 -->
        <div class="question-count-section">
            <div class="section-header">
                <div class="title-with-actions">
                    <span class="section-title">题型数量</span>
                    <div class="section-actions">
                        <span class="action-label">细化</span>
                        <el-switch
                                v-model="detailedMode"
                                class="detail-switch-control"
                        />
                    </div>
                </div>
            </div>
            <el-form class="question-count-form" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="24">
                        <div class="question-count-row">
                            <div v-for="(count, type) in questionCounts" :key="type" class="question-type-item">
                                <span class="question-type-label">{{ getQuestionTypeLabel(type) }}</span>
                                <el-input
                                        v-model.number="questionCounts[type]"
                                        :max="30"
                                        :min="0"
                                        class="question-count-input"
                                        placeholder="数量"
                                        type="number"
                                        @change="handleQuestionCountChange"
                                />
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-form>
        </div>

        <!-- 题型配置区域 -->
        <div v-if="hasQuestions && !detailedMode" class="question-config-section">
            <div class="section-header">
                <span class="section-title">题型配置</span>
            </div>
            <el-card class="question-type-card" shadow="hover">
                <el-table :data="Object.keys(questionCounts).filter(type => questionCounts[type] > 0)" border
                          style="width: 100%">
                    <el-table-column label="题型" width="80">
                        <template #default="scope">
                            <dict-tag :options="sys_qh_questions_type" :value="questionTypeMapping[scope.row]"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="数量" width="60">
                        <template #default="scope">
                            <span>{{ questionCounts[scope.row] }}道</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="年级" width="120">
                        <template #default="scope">
                            <el-select v-model="typeConfigs[scope.row].gradeId" placeholder="选择年级"
                                       @change="applyTypeConfig(scope.row)">
                                <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value"/>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="试卷类型" width="120">
                        <template #default="scope">
                            <el-select v-model="typeConfigs[scope.row].paperType" placeholder="选择类型"
                                       @change="applyTypeConfig(scope.row)">
                                <el-option v-for="item in paperTypeOptions" :key="item.value" :label="item.label"
                                           :value="item.value"/>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="难度" width="120">
                        <template #default="scope">
                            <el-select v-model="typeConfigs[scope.row].difficulty" placeholder="选择难度"
                                       @change="applyTypeConfig(scope.row)">
                                <el-option v-for="item in difficultyOptions" :key="item.value" :label="item.label"
                                           :value="item.value"/>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="知识点" width="125">
                        <template #default="scope">
                            <el-button link type="primary" @click="showTypeKnowledgeDialog(scope.row)">
                                {{
                                typeConfigs[scope.row].knowledgePoints.length > 0 ? `已选择${typeConfigs[scope.row].knowledgePoints.length}个知识点` : '选择知识点'
                                }}
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="章节" width="120">
                        <template #default="scope">
                            <el-button link type="primary" @click="showTypeChapterDialog(scope.row)">
                                {{
                                typeConfigs[scope.row].chapters.length > 0 ? `已选择${typeConfigs[scope.row].chapters.length}个章节` : '选择章节'
                                }}
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="地区" width="120">
                        <template #default="scope">
                            <el-input v-model="typeConfigs[scope.row].region" placeholder="输入地区"
                                      @change="applyTypeConfig(scope.row)"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="年份" width="120">
                        <template #default="scope">
                            <el-input v-model="typeConfigs[scope.row].year" placeholder="输入年份"
                                      @change="applyTypeConfig(scope.row)"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="标签">
                        <template #default="scope">
                            <div class="tags-container">
                                <el-tag
                                        v-for="tag in typeConfigs[scope.row].tags"
                                        :key="tag"
                                        class="question-tag"
                                        closable
                                        @close="handleRemoveTypeTag(scope.row, tag)"
                                >
                                    {{ tag }}
                                </el-tag>
                                <el-input
                                        v-if="typeConfigs[scope.row].tagInputVisible"
                                        ref="typeTagInputRef"
                                        v-model="typeConfigs[scope.row].tagInputValue"
                                        class="tag-input"
                                        size="small"
                                        @blur="handleTypeTagConfirm(scope.row)"
                                        @keyup.enter="handleTypeTagConfirm(scope.row)"
                                />
                                <el-button v-else size="small" @click="showTypeTagInput(scope.row)">
                                    + 添加标签
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column label="单题分值" width="150">
                        <template #default="scope">
                            <el-input-number v-model="typeConfigs[scope.row].score" :max="100" :min="1" size="small"
                                             @change="applyTypeConfig(scope.row)"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="小计" width="90">
                        <template #default="scope">
                            <span class="total-type-score">{{ typeScores[scope.row] }}分</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="50">
                        <template #default="scope">
                            <el-tooltip content="删除" placement="top">
                                <el-button icon="Delete" link type="primary" @click="removeQuestionType(scope.row)"></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </div>

        <!-- 题型配置区域 - 按题目详细配置 -->
        <div v-if="hasQuestions && detailedMode" class="question-config-section">
            <div class="section-header">
                <span class="section-title">题型配置</span>
            </div>
            <div v-for="(type, typeIndex) in Object.keys(questionCounts)" v-show="questionCounts[type] > 0" :key="type">
                <div class="type-header">
                    <dict-tag :options="sys_qh_questions_type" :value="questionTypeMapping[type]" class="type-title-tag"/>
                    <span class="type-subtitle">（共{{ questionCounts[type] }}题，{{ typeScores[type] }}分）</span>
                </div>

                <el-table :data="getQuestionsByType(type)" border style="width: 100%; margin-bottom: 20px;">
                    <el-table-column label="题号" width="60">
                        <template #default="scope">
                            {{ `${typeIndex + 1}-${scope.$index + 1}` }}
                        </template>
                    </el-table-column>
                    <el-table-column label="年级" width="120">
                        <template #default="scope">
                            <el-select v-model="scope.row.gradeId" placeholder="选择年级">
                                <el-option v-for="item in gradeOptions" :key="item.value" :label="item.label" :value="item.value"/>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="试卷类型" width="120">
                        <template #default="scope">
                            <el-select v-model="scope.row.paperType" placeholder="选择类型">
                                <el-option v-for="item in paperTypeOptions" :key="item.value" :label="item.label"
                                           :value="item.value"/>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="难度" width="120">
                        <template #default="scope">
                            <el-select v-model="scope.row.difficulty" placeholder="选择难度">
                                <el-option v-for="item in difficultyOptions" :key="item.value" :label="item.label"
                                           :value="item.value"/>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column label="知识点" width="140">
                        <template #default="scope">
                            <el-button link type="primary" @click="showKnowledgeDialog(scope.row)">
                                {{
                                scope.row.knowledgePoints.length > 0 ? `已选择${scope.row.knowledgePoints.length}个知识点` : '选择知识点'
                                }}
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="章节" width="140">
                        <template #default="scope">
                            <el-button link type="primary" @click="showChapterDialog(scope.row)">
                                {{ scope.row.chapters.length > 0 ? `已选择${scope.row.chapters.length}个章节` : '选择章节' }}
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="地区" width="140">
                        <template #default="scope">
                            <el-input v-model="scope.row.region" placeholder="输入地区"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="年份" width="140">
                        <template #default="scope">
                            <el-input v-model="scope.row.year" placeholder="输入年份"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="标签">
                        <template #default="scope">
                            <el-tag
                                    v-for="tag in scope.row.tags"
                                    :key="tag"
                                    class="question-tag"
                                    closable
                                    @close="handleRemoveTag(scope.row, tag)"
                            >
                                {{ tag }}
                            </el-tag>
                            <el-input
                                    v-if="scope.row.tagInputVisible"
                                    ref="tagInputRef"
                                    v-model="scope.row.tagInputValue"
                                    class="tag-input"
                                    size="small"
                                    @blur="handleTagConfirm(scope.row)"
                                    @keyup.enter="handleTagConfirm(scope.row)"
                            />
                            <el-button v-else size="small" @click="showTagInput(scope.row)">
                                + 添加标签
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="分值" width="150">
                        <template #default="scope">
                            <el-input-number v-model="scope.row.score" :max="100" :min="1" size="small"
                                             @change="calculateTotalScore"/>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="50">
                        <template #default="scope">
                            <el-tooltip content="删除" placement="top">
                                <el-button icon="Delete" link type="primary" @click="removeQuestion(type, scope.$index)"></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>

        <!-- 操作按钮区 -->
        <div class="action-buttons">
            <div v-if="hasQuestions" class="total-score">
                总分：<span class="score-value">{{ totalScore }}</span>
            </div>
            <div class="buttons-group">
                <el-button @click="resetForm">重置</el-button>
                <el-button type="primary" @click="generatePaper">组卷</el-button>
            </div>
        </div>

        <!-- 知识点选择对话框 -->
        <el-dialog v-model="knowledgeDialogVisible" title="选择知识点" width="50%">
            <el-tree
                    ref="knowledgeTreeRef"
                    :data="knowledgeTreeData"
                    :default-checked-keys="currentQuestion?.knowledgePoints || []"
                    node-key="id"
                    show-checkbox
                    :props="{
            label: 'label',
            children: 'children'
          }"
            />
            <template #footer>
        <span class="dialog-footer">
          <el-button @click="knowledgeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmKnowledgePoints">确认</el-button>
        </span>
            </template>
        </el-dialog>

        <!-- 题型知识点选择对话框 -->
        <el-dialog v-model="typeKnowledgeDialogVisible" title="选择知识点" width="50%">
            <el-tree
                    ref="typeKnowledgeTreeRef"
                    :data="knowledgeTreeData"
                    :default-checked-keys="currentType ? typeConfigs[currentType].knowledgePoints : []"
                    node-key="id"
                    show-checkbox
                    :props="{
            label: 'label',
            children: 'children'
          }"
            />
            <template #footer>
        <span class="dialog-footer">
          <el-button @click="typeKnowledgeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmTypeKnowledgePoints">确认</el-button>
        </span>
            </template>
        </el-dialog>

        <!-- 章节选择对话框 -->
        <el-dialog v-model="chapterDialogVisible" title="选择章节" width="50%">
            <el-tree
                    ref="chapterTreeRef"
                    :data="chapterTreeData"
                    :default-checked-keys="currentQuestion?.chapters || []"
                    node-key="id"
                    show-checkbox
                    :props="{
            label: 'label',
            children: 'children'
          }"
            />
            <template #footer>
        <span class="dialog-footer">
          <el-button @click="chapterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmChapterPoints">确认</el-button>
        </span>
            </template>
        </el-dialog>

        <!-- 题型章节选择对话框 -->
        <el-dialog v-model="typeChapterDialogVisible" title="选择章节" width="50%">
            <el-tree
                    ref="typeChapterTreeRef"
                    :data="chapterTreeData"
                    :default-checked-keys="currentType ? typeConfigs[currentType].chapters : []"
                    node-key="id"
                    show-checkbox
                    :props="{
            label: 'label',
            children: 'children'
          }"
            />
            <template #footer>
        <span class="dialog-footer">
          <el-button @click="typeChapterDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmTypeChapterPoints">确认</el-button>
        </span>
            </template>
        </el-dialog>
    </div>
</template>

<script name="PaperIndex" setup>
    import {computed, getCurrentInstance, nextTick, onMounted, reactive, ref} from 'vue'
    import {ElLoading, ElMessage} from 'element-plus'
    import request from '@/utils/request'
    import {useRouter} from 'vue-router'

    const router = useRouter()
    const {proxy} = getCurrentInstance()
    const {sys_qh_questions_type, sys_qh_difficulty} = proxy.useDict("sys_qh_questions_type", "sys_qh_difficulty")

    // 表单数据
    const paperFormRef = ref(null)
    const paperForm = reactive({
        gradeId: '',
        paperType: '',
        paperName: '',
        libraryIds: []
    })

    // 表单验证规则
    const rules = {
        paperName: [{required: true, message: '请输入试卷名称', trigger: 'blur'}],
        libraryIds: [{type: 'array', required: false, message: '请选择题库', trigger: 'change'}]
    }

    // 模拟数据
    const libraryOptions = ref([])
    const gradeOptions = ref([])

    // 使用字典数据的难度选项
    const difficultyOptions = computed(() => {
        return sys_qh_difficulty.value.map(item => ({
            value: item.value,
            label: item.label
        }));
    })

    // 试卷类型选项
    const paperTypeOptions = [
        {value: '期中卷', label: '期中卷'},
    ]

    // 细化模式开关
    const detailedMode = ref(false)

    // 题型定义 (可以保留作为备用)
    const questionTypeLabels = {
        singleChoice: '单选题',
        multiChoice: '多选题',
        judgment: '判断题',
        fillBlank: '填空题',
        shortAnswer: '解答题'
    }

    // 题型映射关系
    const questionTypeMapping = {
        singleChoice: '1',
        multiChoice: '2',
        judgment: '3',
        fillBlank: '4',
        shortAnswer: '5'
    }

    // 获取题型标签
    const getQuestionTypeLabel = (type) => {
        const typeCode = questionTypeMapping[type]
        const dictItem = sys_qh_questions_type.value.find(item => item.value === typeCode)
        return dictItem ? dictItem.label : questionTypeLabels[type]
    }

    // 题型数量
    const questionCounts = reactive({
        singleChoice: 0,
        multiChoice: 0,
        judgment: 0,
        fillBlank: 0,
        shortAnswer: 0
    })

    // 简化模式下的题型配置
    const defaultDifficulty = computed(() => sys_qh_difficulty.value.length > 0 ? sys_qh_difficulty.value[0].value : '')

    const typeConfigs = reactive({
        singleChoice: {
            difficulty: '',
            gradeId: '',
            paperType: '',
            knowledgePoints: [],
            chapters: [],
            region: '',
            year: '',
            tags: [],
            tagInputVisible: false,
            tagInputValue: '',
            score: 3
        },
        multiChoice: {
            difficulty: '',
            gradeId: '',
            paperType: '',
            knowledgePoints: [],
            chapters: [],
            region: '',
            year: '',
            tags: [],
            tagInputVisible: false,
            tagInputValue: '',
            score: 4
        },
        judgment: {
            difficulty: '',
            gradeId: '',
            paperType: '',
            knowledgePoints: [],
            chapters: [],
            region: '',
            year: '',
            tags: [],
            tagInputVisible: false,
            tagInputValue: '',
            score: 2
        },
        fillBlank: {
            difficulty: '',
            gradeId: '',
            paperType: '',
            knowledgePoints: [],
            chapters: [],
            region: '',
            year: '',
            tags: [],
            tagInputVisible: false,
            tagInputValue: '',
            score: 3
        },
        shortAnswer: {
            difficulty: '',
            gradeId: '',
            paperType: '',
            knowledgePoints: [],
            chapters: [],
            region: '',
            year: '',
            tags: [],
            tagInputVisible: false,
            tagInputValue: '',
            score: 5
        }
    })

    // 设置初始难度值
    const initDifficultyValues = () => {
        const defaultValue = defaultDifficulty.value
        for (const type in typeConfigs) {
            typeConfigs[type].difficulty = defaultValue
        }
    }

    // 题目列表
    const questions = reactive({
        singleChoice: [],
        multiChoice: [],
        judgment: [],
        fillBlank: [],
        shortAnswer: []
    })

    // 计算属性：是否有题目
    const hasQuestions = computed(() => {
        return Object.values(questionCounts).some(count => count > 0)
    })

    // 计算属性：每种题型的总分
    const typeScores = computed(() => {
        const scores = {}
        for (const type in questions) {
            scores[type] = questions[type].reduce((sum, q) => sum + q.score, 0)
        }
        return scores
    })

    // 计算属性：总分
    const totalScore = computed(() => {
        let sum = 0
        for (const type in questions) {
            sum += questions[type].reduce((acc, q) => acc + q.score, 0)
        }
        return sum
    })

    // 当题目数量变化时
    const handleQuestionCountChange = () => {
        for (const type in questionCounts) {
            // 确保数量是有效数字且在范围内
            if (isNaN(questionCounts[type]) || questionCounts[type] < 0) {
                questionCounts[type] = 0;
            } else if (questionCounts[type] > 30) {
                questionCounts[type] = 30;
            } else {
                // 将输入转换为整数
                questionCounts[type] = Math.floor(questionCounts[type]);
            }

            const oldCount = questions[type].length;
            const newCount = questionCounts[type];

            if (newCount > oldCount) {
                // 增加题目
                for (let i = oldCount; i < newCount; i++) {
                    questions[type].push({
                        id: `${type}-${i + 1}`,
                        difficulty: typeConfigs[type].difficulty, // 使用题型默认配置
                        gradeId: typeConfigs[type].gradeId, // 使用题型默认年级
                        paperType: typeConfigs[type].paperType, // 使用题型默认试卷类型
                        knowledgePoints: [...typeConfigs[type].knowledgePoints], // 深拷贝知识点
                        chapters: [...typeConfigs[type].chapters], // 深拷贝章节
                        region: typeConfigs[type].region, // 使用题型默认地区
                        year: typeConfigs[type].year, // 使用题型默认年份
                        tags: [...typeConfigs[type].tags], // 深拷贝标签
                        tagInputVisible: false,
                        tagInputValue: '',
                        score: typeConfigs[type].score // 使用题型默认分值
                    });
                }
            } else if (newCount < oldCount) {
                // 减少题目
                questions[type].splice(newCount, oldCount - newCount);
            }
        }
        calculateTotalScore();
    }

    // 应用题型配置到所有该类型题目
    const applyTypeConfig = (type) => {
        const config = typeConfigs[type]

        questions[type].forEach(question => {
            question.difficulty = config.difficulty
            question.gradeId = config.gradeId
            question.paperType = config.paperType
            question.region = config.region
            question.year = config.year
            question.score = config.score
            // 知识点、章节和标签不进行批量修改，保持各题目的独立性
        })

        calculateTotalScore()
    }

    // 默认分值
    const getDefaultScore = (type) => {
        return typeConfigs[type].score || 2
    }

    // 获取特定类型的题目
    const getQuestionsByType = (type) => {
        return questions[type]
    }

    // 计算总分
    const calculateTotalScore = () => {
        // 计算已经在computed中完成
    }

    // 删除题目
    const removeQuestion = (type, index) => {
        questions[type].splice(index, 1)
        questionCounts[type] -= 1
        calculateTotalScore()
    }

    // 删除整个题型
    const removeQuestionType = (type) => {
        questionCounts[type] = 0
        questions[type] = []
        calculateTotalScore()
    }

    // 知识点选择相关
    let knowledgeDialogVisible = ref(false)
    const knowledgeTreeRef = ref(null)
    const currentQuestion = ref(null)

    // 题型知识点选择相关
    let typeKnowledgeDialogVisible = ref(false)
    const typeKnowledgeTreeRef = ref(null)
    const currentType = ref(null)

    // 模拟的知识树数据
    const knowledgeTreeData = ref([])

    // 显示知识点对话框
    const showKnowledgeDialog = (question) => {
        currentQuestion.value = question
        knowledgeDialogVisible.value = true
    }

    // 确认选择的知识点
    const confirmKnowledgePoints = () => {
        if (currentQuestion.value && knowledgeTreeRef.value) {
            currentQuestion.value.knowledgePoints = knowledgeTreeRef.value.getCheckedKeys()
        }
        knowledgeDialogVisible.value = false
    }

    // 显示题型知识点对话框
    const showTypeKnowledgeDialog = (type) => {
        currentType.value = type
        typeKnowledgeDialogVisible.value = true
    }

    // 确认选择的题型知识点
    const confirmTypeKnowledgePoints = () => {
        if (currentType.value && typeKnowledgeTreeRef.value) {
            const type = currentType.value
            typeConfigs[type].knowledgePoints = typeKnowledgeTreeRef.value.getCheckedKeys()

            // 如果希望自动应用到所有该类型的题目，可以取消注释下面的代码
            // questions[type].forEach(question => {
            //   question.knowledgePoints = [...typeConfigs[type].knowledgePoints]
            // })
        }
        typeKnowledgeDialogVisible.value = false
    }

    // 标签相关
    const tagInputRef = ref(null)
    const typeTagInputRef = ref(null)

    // 显示标签输入框
    const showTagInput = (question) => {
        question.tagInputVisible = true
        nextTick(() => {
            tagInputRef.value?.focus()
        })
    }

    // 确认添加标签
    const handleTagConfirm = (question) => {
        const inputValue = question.tagInputValue
        if (inputValue && !question.tags.includes(inputValue)) {
            question.tags.push(inputValue)
        }
        question.tagInputVisible = false
        question.tagInputValue = ''
    }

    // 移除标签
    const handleRemoveTag = (question, tag) => {
        question.tags.splice(question.tags.indexOf(tag), 1)
    }

    // 显示题型标签输入框
    const showTypeTagInput = (type) => {
        typeConfigs[type].tagInputVisible = true
        nextTick(() => {
            typeTagInputRef.value?.focus()
        })
    }

    // 确认添加题型标签
    const handleTypeTagConfirm = (type) => {
        const inputValue = typeConfigs[type].tagInputValue
        if (inputValue && !typeConfigs[type].tags.includes(inputValue)) {
            typeConfigs[type].tags.push(inputValue)
        }
        typeConfigs[type].tagInputVisible = false
        typeConfigs[type].tagInputValue = ''
    }

    // 移除题型标签
    const handleRemoveTypeTag = (type, tag) => {
        typeConfigs[type].tags.splice(typeConfigs[type].tags.indexOf(tag), 1)
    }

    // 章节选择相关
    let chapterDialogVisible = ref(false)
    const chapterTreeRef = ref(null)
    let typeChapterDialogVisible = ref(false)
    const typeChapterTreeRef = ref(null)

    // 模拟的章节树数据
    const chapterTreeData = ref([])

    // 显示章节对话框
    const showChapterDialog = (question) => {
        currentQuestion.value = question
        chapterDialogVisible.value = true
    }

    // 确认选择的章节
    const confirmChapterPoints = () => {
        if (currentQuestion.value && chapterTreeRef.value) {
            currentQuestion.value.chapters = chapterTreeRef.value.getCheckedKeys()
        }
        chapterDialogVisible.value = false
    }

    // 显示题型章节对话框
    const showTypeChapterDialog = (type) => {
        currentType.value = type
        typeChapterDialogVisible.value = true
    }

    // 确认选择的题型章节
    const confirmTypeChapterPoints = () => {
        if (currentType.value && typeChapterTreeRef.value) {
            const type = currentType.value
            typeConfigs[type].chapters = typeChapterTreeRef.value.getCheckedKeys()
        }
        typeChapterDialogVisible.value = false
    }

    // 重置表单
    const resetForm = () => {
        paperFormRef.value?.resetFields()
        detailedMode.value = false

        for (const type in questionCounts) {
            questionCounts[type] = 0
        }

        for (const type in questions) {
            questions[type] = []
        }

        // 重置题型配置并设置默认难度
        for (const type in typeConfigs) {
            typeConfigs[type] = {
                difficulty: defaultDifficulty.value,
                gradeId: '',
                paperType: '',
                knowledgePoints: [],
                chapters: [],
                region: '',
                year: '',
                tags: [],
                tagInputVisible: false,
                tagInputValue: '',
                score: getDefaultScore(type)
            }
        }
    }

    // 处理测试数据（模拟接口返回）
    const processTestData = (testData) => {
        if (testData && testData.code === 200 && Array.isArray(testData.data)) {
            // 不再显示预览对话框
            ElMessage.success('自动组卷成功！')
            router.push('/cl/test');
            return true
        }
        return false
    }

    // 开始组卷
    const generatePaper = () => {
        paperFormRef.value?.validate((valid) => {
            if (valid) {
                if (!hasQuestions.value) {
                    ElMessage.warning('请至少添加一道题目')
                    return
                }

                // 构建符合后端DTO要求的参数
                const questionDTOList = []

                // 处理每种题型
                for (const type in questionCounts) {
                    if (questionCounts[type] > 0) {
                        // 使用字典映射获取题型代码
                        const questionTypeCode = questionTypeMapping[type]

                        // 如果是非细化模式，使用题型配置
                        if (!detailedMode.value) {
                            questionDTOList.push({
                                questionType: questionTypeCode,
                                requiredNumber: questionCounts[type],
                                difficulty: typeConfigs[type].difficulty,
                                score: typeConfigs[type].score.toString(),
                                knowledgePoints: typeConfigs[type].knowledgePoints.map(String),
                                chapters: typeConfigs[type].chapters.map(String),
                                region: typeConfigs[type].region,
                                year: typeConfigs[type].year,
                                gradeId: typeConfigs[type].gradeId,
                                paperType: typeConfigs[type].paperType,
                                tags: typeConfigs[type].tags,
                                // libraryIds: paperForm.libraryIds // 将题库ID添加到每个DTO中
                            })
                        } else {
                            // 细化模式，为每个题目创建一个DTO
                            questions[type].forEach(question => {
                                questionDTOList.push({
                                    questionType: questionTypeCode,
                                    requiredNumber: 1,
                                    difficulty: question.difficulty,
                                    score: question.score.toString(),
                                    knowledgePoints: question.knowledgePoints.map(String),
                                    chapters: question.chapters.map(String),
                                    region: question.region,
                                    year: question.year,
                                    gradeId: question.gradeId,
                                    paperType: question.paperType,
                                    tags: question.tags,
                                    // libraryIds: paperForm.libraryIds // 将题库ID添加到每个DTO中
                                })
                            })
                        }
                    }
                }

                // 符合QhGeneratePaperDTO格式的参数
                const paperData = {
                    paperName: paperForm.paperName,
                    flag: detailedMode.value ? '1' : '0',
                    libraryIds: paperForm.libraryIds, // 将题库ID添加到与paperName同级
                    questionDTOList: questionDTOList
                }

                // 调用API生成试卷
                const loading = ElLoading.service({
                    lock: true,
                    text: '正在组卷中，请稍候...',
                    background: 'rgba(0, 0, 0, 0.7)'
                })

                request({
                    url: '/qh/paper/test',
                    method: 'post',
                    data: paperData
                }).then(response => {
                    loading.close()
                    if (processTestData(response)) {
                    } else {
                        ElMessage.error(response.msg || '组卷失败，请重试')
                    }
                }).catch(error => {
                    loading.close()
                })
            }
        })
    }

    // 将后端返回的数据转换为树状结构
    const convertToTreeData = (data) => {
        if (!data || !Array.isArray(data)) return []

        // 检查数据是否已经是树结构（有children属性）
        const isTreeStructure = data.some(item => item.children && Array.isArray(item.children))

        if (isTreeStructure) {
            // 数据已经是树结构，只需确保每个节点都有正确的label和children属性
            const processTree = (items) => {
                return items.map(item => {
                    // 复制节点，确保不直接修改原始数据
                    const node = {
                        ...item,
                        label: item.label || item.name, // 优先使用label，如果没有则使用name
                        id: item.id
                    }

                    // 确保每个节点都有children属性
                    if (!node.children) {
                        node.children = []
                    } else if (Array.isArray(node.children) && node.children.length > 0) {
                        // 递归处理子节点
                        node.children = processTree(node.children)
                    }

                    return node
                })
            }

            return processTree(data)
        } else {
            // 数据是扁平结构，需要根据parentId构建树结构
            const result = []
            const map = {}

            // 第一遍：创建所有节点的映射
            data.forEach(item => {
                map[item.id] = {
                    id: item.id,
                    label: item.label || item.name,
                    children: []
                }
            })

            // 第二遍：构建树结构
            data.forEach(item => {
                const node = map[item.id]

                // 如果有父节点且不是顶级节点
                if (item.parentId && item.parentId !== '0' && map[item.parentId]) {
                    // 将当前节点添加到父节点的children中
                    map[item.parentId].children.push(node)
                } else {
                    // 顶级节点直接添加到结果数组
                    result.push(node)
                }
            })

            return result
        }
    }

    // 获取题库数据
    const fetchLibraryOptions = () => {
        request({
            url: '/qh/knowledgeTree/nodeType/1',
            method: 'get'
        }).then(response => {
            if (response.code === 200 && Array.isArray(response.data)) {
                libraryOptions.value = response.data.map(item => ({
                    value: item.id,
                    label: item.label || item.name // 使用label或name作为显示标签
                }))
            } else {
                ElMessage.error(response.msg || '获取题库数据失败')
            }
        })
    }

    // 获取年级数据
    const fetchGradeOptions = () => {
        request({
            url: '/qh/knowledgeTree/nodeType/2',
            method: 'get'
        }).then(response => {
            if (response.code === 200 && Array.isArray(response.data)) {
                gradeOptions.value = response.data.map(item => ({
                    value: item.id,
                    label: item.label || item.name // 使用label或name作为显示标签
                }))
            } else {
                ElMessage.error(response.msg || '获取年级数据失败')
            }
        })
    }

    // 获取章节数据
    const fetchChapterData = () => {
        request({
            url: '/qh/knowledgeTree/nodeType/4',
            method: 'get'
        }).then(response => {
            if (response.code === 200 && Array.isArray(response.data)) {
                chapterTreeData.value = convertToTreeData(response.data)
            } else {
                ElMessage.error(response.msg || '获取章节数据失败')
            }
        })
    }

    // 获取知识点数据
    const fetchKnowledgeData = () => {
        request({
            url: '/qh/knowledgeTree/nodeType/5',
            method: 'get'
        }).then(response => {
            if (response.code === 200 && Array.isArray(response.data)) {
                knowledgeTreeData.value = convertToTreeData(response.data)
            } else {
                ElMessage.error(response.msg || '获取知识点数据失败')
            }
        })
    }

    // 页面加载时进行初始化
    onMounted(() => {
        // 获取数据
        fetchLibraryOptions()
        fetchGradeOptions()
        fetchChapterData()
        fetchKnowledgeData()
        // 初始化难度值
        initDifficultyValues()
    })
</script>

<style scoped>
    .app-container {
        padding: 20px;
        background-color: #fff;
    }

    /* 移除paper-card的样式，改为在app-container中应用样式 */
    .basic-info-section, .question-count-section, .question-config-section {
        margin-bottom: 30px;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }

    .title-with-actions {
        display: flex;
        align-items: center;
    }

    .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
    }

    .section-actions {
        display: flex;
        align-items: center;
        margin-left: 15px;
    }

    .action-label {
        margin-right: 10px;
        font-size: 14px;
        font-weight: bold;
        color: #606266;
    }

    .form-item-width {
        width: 100%;
        max-width: 400px;
    }

    .count-card {
        text-align: center;
        margin-bottom: 10px;
    }

    .count-title {
        margin-bottom: 10px;
        font-weight: bold;
    }

    .type-header {
        margin: 15px 0;
    }

    .type-title {
        font-size: 16px;
        font-weight: bold;
        color: #409EFF;
    }

    .type-subtitle {
        font-size: 14px;
        color: #606266;
        margin-left: 10px;
    }

    .type-title-inline {
        font-weight: bold;
        color: #409EFF;
        padding: 2px 8px;
        background-color: #ecf5ff;
        border-radius: 4px;
        display: inline-block;
    }

    .type-subtitle-inline {
        font-size: 12px;
        color: #606266;
        margin-top: 4px;
    }

    .question-tag {
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .tag-input {
        width: 150px;
        margin-left: 8px;
        vertical-align: bottom;
    }

    .action-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #ebeef5;
    }

    .total-score {
        font-size: 16px;
    }

    .score-value {
        font-size: 20px;
        font-weight: bold;
        color: #f56c6c;
    }

    .buttons-group {
        display: flex;
        gap: 10px;
    }

    .question-count-form {
        width: 100%;
    }

    .question-count-row {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        align-items: center;
        gap: 20px;
        width: 100%;
    }

    .question-type-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .question-type-label {
        margin-right: 10px;
        min-width: 10px;
    }

    .detail-switch-control {
        height: 10px; /* 与小型input-number高度一致 */
    }

    /* 新增样式 */
    .question-type-card {
        margin-bottom: 20px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
    }

    .config-row {
        margin-top: 15px;
    }

    .config-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .config-label {
        min-width: 80px;
        font-weight: bold;
    }

    .tags-item {
        display: flex;
        align-items: flex-start;
    }

    .tags-container {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }

    .total-type-score {
        font-weight: bold;
        color: #f56c6c;
    }

    .type-title-tag {
        font-size: 16px;
        margin-right: 8px;
    }

    .question-count-input {
        width: 70px;
        margin-left: -1px;
        height: 25px;
    }
</style>
