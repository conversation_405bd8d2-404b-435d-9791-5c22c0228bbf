<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup>
import {nextTick, onMounted, onUnmounted, ref, watch} from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const chartRef = ref(null)
let chart = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 如果图表已经存在，销毁它
  if (chart) {
    chart.dispose()
  }

  // 创建新的图表实例
  chart = echarts.init(chartRef.value)

  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.data.labels
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: props.data.datasets[0].label,
        type: 'line',
        stack: 'Total',
        smooth: true,
        lineStyle: {
          width: 3,
          color: props.data.datasets[0].borderColor
        },
        showSymbol: false,
        areaStyle: {
          opacity: 0.8,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: props.data.datasets[0].borderColor
            },
            {
              offset: 1,
              color: 'rgba(255, 255, 255, 0.3)'
            }
          ])
        },
        emphasis: {
          focus: 'series'
        },
        data: props.data.datasets[0].data
      }
    ]
  }

  // 应用配置和数据
  chart.setOption(option)

  // 响应窗口大小变化
  window.addEventListener('resize', () => {
    chart && chart.resize()
  })
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    initChart()
  })
}, {deep: true})

// 组件挂载时初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载时清理
onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', () => {
    chart && chart.resize()
  })
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
