<template>
  <div ref="chartRef" class="chart-container"></div>
</template>

<script setup>
import {nextTick, onMounted, onUnmounted, ref, watch} from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  data: {
    type: Object,
    required: true
  }
})

const chartRef = ref(null)
let chart = null

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 如果图表已经存在，销毁它
  if (chart) {
    chart.dispose()
  }

  // 创建新的图表实例
  chart = echarts.init(chartRef.value)

  // 设置图表选项
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'horizontal',
      bottom: 10,
      data: props.data.labels
    },
    series: [
      {
        name: '试题类型',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.labels.map((label, index) => ({
          value: props.data.datasets[0].data[index],
          name: label,
          itemStyle: {
            color: props.data.datasets[0].backgroundColor[index]
          }
        }))
      }
    ]
  }

  // 应用配置和数据
  chart.setOption(option)

  // 响应窗口大小变化
  window.addEventListener('resize', () => {
    chart && chart.resize()
  })
}

// 监听数据变化
watch(() => props.data, () => {
  nextTick(() => {
    initChart()
  })
}, {deep: true})

// 组件挂载时初始化图表
onMounted(() => {
  initChart()
})

// 组件卸载时清理
onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
  window.removeEventListener('resize', () => {
    chart && chart.resize()
  })
})
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
