<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <!-- 顶部统计卡片 -->
      <el-col v-for="(item, index) in statisticsCards" :key="index" :lg="6" :md="12" :sm="12" :xl="6" :xs="24">
        <el-card :body-style="{ padding: '20px' }" :class="item.colorClass" class="statistics-card">
          <div class="card-content">
            <div class="card-icon">
              <el-icon :size="36">
                <component :is="item.icon"/>
              </el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">{{ item.title }}</div>
              <div class="card-value">{{ item.value }}</div>
              <div class="card-subtitle">
                <span :class="item.trend === 'up' ? 'trend-up' : 'trend-down'">
                  <el-icon v-if="item.trend === 'up'" size="12"><ArrowUp/></el-icon>
                  <el-icon v-else size="12"><ArrowDown/></el-icon>
                  {{ item.changeRate }}%
                </span>
                相比上月
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="chart-row">
      <!-- 左侧图表：试题类型分布 -->
      <el-col :lg="8" :md="12" :sm="24" :xl="8" :xs="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>试题类型分布</span>
              <!--              <el-dropdown>-->
              <!--                <span class="el-dropdown-link">-->
              <!--                  <el-button link type="primary">-->
              <!--                    操作<el-icon class="el-icon&#45;&#45;right"><arrow-down/></el-icon>-->
              <!--                  </el-button>-->
              <!--                </span>-->
              <!--                <template #dropdown>-->
              <!--                  <el-dropdown-menu>-->
              <!--                    <el-dropdown-item>导出数据</el-dropdown-item>-->
              <!--                    <el-dropdown-item>查看明细</el-dropdown-item>-->
              <!--                  </el-dropdown-menu>-->
              <!--                </template>-->
              <!--              </el-dropdown>-->
            </div>
          </template>
          <div class="chart-container">
            <pie-chart ref="pieChartRef" :data="questionTypeData"/>
          </div>
        </el-card>
      </el-col>

      <!-- 中部图表：近6个月试题增长趋势 -->
      <el-col :lg="8" :md="12" :sm="24" :xl="8" :xs="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>试题增长趋势</span>
              <!--              <el-button link type="primary">更多</el-button>-->
            </div>
          </template>
          <div class="chart-container">
            <line-chart ref="lineChartRef" :data="questionTrendData"/>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧图表：试题难度分布 -->
      <el-col :lg="8" :md="12" :sm="24" :xl="8" :xs="24">
        <el-card class="chart-card">
          <template #header>
            <div class="card-header">
              <span>试题难度分布</span>
              <!--              <el-button link type="primary">查看详情</el-button>-->
            </div>
          </template>
          <div class="chart-container">
            <bar-chart ref="barChartRef" :data="questionDifficultyData"/>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-bottom: 20px;">
      <!-- 快速访问链接 -->
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="card-header">
              <span>快速访问</span>
            </div>
          </template>
          <div style="display: flex; gap: 10px;">
            <el-button type="primary" @click="router.push('/qh/paperUpload/index')">试卷上传</el-button>
            <el-button @click="router.push('/qh/paper/index')">自动组卷</el-button>
            <el-button @click="router.push('/qh/questionBank')">题库管理</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <!-- 系统公告 -->
      <el-col :span="24">
        <el-card class="notice-card">
          <!--          <template #header>-->
          <!--            <div class="card-header">-->
          <!--              <span>系统公告</span>-->
          <!--              <el-button link type="primary">更多</el-button>-->
          <!--            </div>-->
          <!--          </template>-->
          <div class="notice-list">
            <div v-for="(notice, index) in systemNotices" :key="index" class="notice-item">
              <div class="notice-title">{{ notice.title }}</div>
              <div class="notice-content" v-html="notice.content"></div>
              <div class="notice-time">{{ notice.time }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import {getCurrentInstance, onMounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ArrowDown, ArrowUp} from '@element-plus/icons-vue'
import PieChart from './components/PieChart.vue'
import LineChart from './components/LineChart.vue'
import BarChart from './components/BarChart.vue'
import {dashboard as userDashboard} from '@/api/system/user'
import {dashboard as questionBankDashboard} from '@/api/qh/questionBank'
import {dashboard as knowledgeTreeDashboard} from '@/api/qh/knowledgeTree.js'
import {dashboard as paperDashboard} from '@/api/qh/paper.js'
import {listNotice} from '@/api/system/notice'

const router = useRouter()
const {proxy} = getCurrentInstance()
const {sys_qh_questions_type} = proxy.useDict("sys_qh_questions_type")

// 顶部统计卡片数据
const statisticsCards = ref([
  {
    title: '用户总数',
    value: undefined,
    icon: 'User',
    trend: 'up',
    changeRate: undefined,
    colorClass: 'user-card'
  },
  {
    title: '试题总数',
    value: undefined,
    icon: 'Tickets',
    trend: 'up',
    changeRate: undefined,
    colorClass: 'question-card'
  },
  {
    title: '试卷总数',
    value: undefined,
    icon: 'DocumentCopy',
    trend: 'up',
    changeRate: undefined,
    colorClass: 'paper-card'
  },
  {
    title: '知识点数',
    value: undefined,
    icon: 'Reading',
    trend: 'down',
    changeRate: undefined,
    colorClass: 'knowledge-card'
  }
])

// 试题类型分布数据
const questionTypeData = ref({
  labels: ['选择题', '填空题', '判断题', '解答题', '作图题'],
  datasets: [
    {
      data: [undefined, undefined, undefined, undefined, undefined],
      backgroundColor: [
        '#4CAF50', '#2196F3', '#FFC107', '#9C27B0', '#FF5722'
      ]
    }
  ]
})

// 近6个月试题增长趋势
const questionTrendData = ref({
  labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
  datasets: [
    {
      label: '新增试题',
      data: [undefined, undefined, undefined, undefined, undefined, undefined],
      borderColor: '#409EFF',
      backgroundColor: 'rgba(64, 158, 255, 0.1)',
      fill: true
    }
  ]
})

// 试题难度分布
const questionDifficultyData = ref({
  labels: ['简单', '中等', '困难', '挑战', '极难'],
  datasets: [
    {
      label: '试题数量',
      data: [undefined, undefined, undefined, undefined, undefined],
      backgroundColor: [
        'rgba(103, 194, 58, 0.6)',
        'rgba(230, 162, 60, 0.6)',
        'rgba(245, 108, 108, 0.6)',
        'rgba(144, 147, 153, 0.6)',
        'rgba(77, 77, 77, 0.6)'
      ]
    }
  ]
})

// 系统公告
const systemNotices = ref([])

// 图表组件引用
const pieChartRef = ref(null)
const lineChartRef = ref(null)
const barChartRef = ref(null)

// 页面跳转
const toQuestionBank = () => {
  router.push('/qh/questionBank')
}

// 获取当前月份的开始和结束日期
const getCurrentMonthDates = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth()

  const beginTime = new Date(currentYear, currentMonth, 1)
  const endTime = new Date(currentYear, currentMonth + 1, 0)

  return {
    beginTime: formatDate(beginTime),
    endTime: formatDate(endTime)
  }
}

// 获取上个月的开始和结束日期
const getLastMonthDates = () => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const lastMonth = now.getMonth() - 1

  const beginTime = new Date(currentYear, lastMonth, 1)
  const endTime = new Date(currentYear, lastMonth + 1, 0)

  return {
    beginTime: formatDate(beginTime),
    endTime: formatDate(endTime)
  }
}

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 计算变化率
const calculateChangeRate = (currentValue, lastValue) => {
  if (!lastValue || lastValue === 0) return 0
  const rate = ((currentValue - lastValue) / lastValue) * 100
  return parseFloat(rate.toFixed(1))
}

// 加载用户仪表板数据
const loadUserDashboardData = async () => {
  try {
    // 获取总用户数
    const totalRes = await userDashboard({
      beginTime: null,
      endTime: null
    })

    // 获取上个月用户数
    const lastMonth = getLastMonthDates()
    const lastMonthRes = await userDashboard({
      beginTime: lastMonth.beginTime,
      endTime: lastMonth.endTime
    })

    // 获取本月用户数
    const currentMonth = getCurrentMonthDates()
    const currentMonthRes = await userDashboard({
      beginTime: currentMonth.beginTime,
      endTime: currentMonth.endTime
    })

    if (totalRes.code === 200) {
      const totalValue = totalRes.data
      const lastMonthValue = lastMonthRes.data || 0
      const currentMonthValue = currentMonthRes.data || 0

      // 计算变化率
      const changeRate = calculateChangeRate(currentMonthValue, lastMonthValue)

      statisticsCards.value[0].value = totalValue.toString()
      statisticsCards.value[0].changeRate = Math.abs(changeRate)
      statisticsCards.value[0].trend = changeRate >= 0 ? 'up' : 'down'
    }
  } catch (error) {
    console.error('获取用户仪表板数据失败:', error)
  }
}

// 加载试题仪表板数据
const loadQuestionBankDashboardData = async () => {
  try {
    // 获取总试题数
    const totalRes = await questionBankDashboard({
      beginTime: null,
      endTime: null
    })

    // 获取上个月试题数
    const lastMonth = getLastMonthDates()
    const lastMonthRes = await questionBankDashboard({
      beginTime: lastMonth.beginTime,
      endTime: lastMonth.endTime
    })

    // 获取本月试题数
    const currentMonth = getCurrentMonthDates()
    const currentMonthRes = await questionBankDashboard({
      beginTime: currentMonth.beginTime,
      endTime: currentMonth.endTime
    })

    if (totalRes.code === 200) {
      const totalValue = totalRes.data
      const lastMonthValue = lastMonthRes.data || 0
      const currentMonthValue = currentMonthRes.data || 0

      // 计算变化率
      const changeRate = calculateChangeRate(currentMonthValue, lastMonthValue)

      statisticsCards.value[1].value = totalValue.toString()
      statisticsCards.value[1].changeRate = Math.abs(changeRate)
      statisticsCards.value[1].trend = changeRate >= 0 ? 'up' : 'down'
    }
  } catch (error) {
    console.error('获取试题仪表板数据失败:', error)
  }
}

// 加载试卷仪表板数据
const loadPaperDashboardData = async () => {
  try {
    // 获取总试卷数
    const totalRes = await paperDashboard({
      beginTime: null,
      endTime: null
    })

    // 获取上个月试卷数
    const lastMonth = getLastMonthDates()
    const lastMonthRes = await paperDashboard({
      beginTime: lastMonth.beginTime,
      endTime: lastMonth.endTime
    })

    // 获取本月试卷数
    const currentMonth = getCurrentMonthDates()
    const currentMonthRes = await paperDashboard({
      beginTime: currentMonth.beginTime,
      endTime: currentMonth.endTime
    })

    if (totalRes.code === 200) {
      const totalValue = totalRes.data
      const lastMonthValue = lastMonthRes.data || 0
      const currentMonthValue = currentMonthRes.data || 0

      // 计算变化率
      const changeRate = calculateChangeRate(currentMonthValue, lastMonthValue)

      statisticsCards.value[2].value = totalValue.toString()
      statisticsCards.value[2].changeRate = Math.abs(changeRate)
      statisticsCards.value[2].trend = changeRate >= 0 ? 'up' : 'down'
    }
  } catch (error) {
    console.error('获取试卷仪表板数据失败:', error)
  }
}

// 加载知识点仪表板数据
const loadKnowledgeTreeDashboardData = async () => {
  try {
    // 获取总知识点数
    const totalRes = await knowledgeTreeDashboard({
      beginTime: null,
      endTime: null
    })

    // 获取上个月知识点数
    const lastMonth = getLastMonthDates()
    const lastMonthRes = await knowledgeTreeDashboard({
      beginTime: lastMonth.beginTime,
      endTime: lastMonth.endTime
    })

    // 获取本月知识点数
    const currentMonth = getCurrentMonthDates()
    const currentMonthRes = await knowledgeTreeDashboard({
      beginTime: currentMonth.beginTime,
      endTime: currentMonth.endTime
    })

    if (totalRes.code === 200) {
      const totalValue = totalRes.data
      const lastMonthValue = lastMonthRes.data || 0
      const currentMonthValue = currentMonthRes.data || 0

      // 计算变化率
      const changeRate = calculateChangeRate(currentMonthValue, lastMonthValue)

      statisticsCards.value[3].value = totalValue.toString()
      statisticsCards.value[3].changeRate = Math.abs(changeRate)
      statisticsCards.value[3].trend = changeRate >= 0 ? 'up' : 'down'
    }
  } catch (error) {
    console.error('获取知识点仪表板数据失败:', error)
  }
}

// 获取指定月份的开始和结束日期
const getMonthDates = (year, month) => {
  const beginTime = new Date(year, month - 1, 1)
  const endTime = new Date(year, month, 0)

  return {
    beginTime: formatDate(beginTime),
    endTime: formatDate(endTime)
  }
}

// 加载试题月度增长趋势数据
const loadQuestionTrendData = async () => {
  try {
    const currentDate = new Date()
    const currentYear = currentDate.getFullYear()
    const currentMonth = currentDate.getMonth() + 1 // 当前月，JavaScript中月份从0开始

    const monthData = []
    const monthLabels = []

    // 获取包括当前月在内往前3个月的数据
    for (let i = 0; i < 3; i++) {
      // 计算月份，处理跨年情况
      let month = currentMonth - i
      let year = currentYear

      if (month <= 0) {
        month += 12
        year -= 1
      }

      const monthDates = getMonthDates(year, month)
      const res = await questionBankDashboard({
        beginTime: monthDates.beginTime,
        endTime: monthDates.endTime
      })

      // 格式化月份标签，添加年份信息（如果不是当年）
      let monthLabel = month + '月'
      if (year !== currentYear) {
        monthLabel = year + '年' + monthLabel
      }

      // 添加月份标签和数据（从最早的月份开始排序）
      monthLabels.unshift(monthLabel)

      if (res.code === 200) {
        monthData.unshift(res.data || 0)
      } else {
        monthData.unshift(0)
      }
    }

    // 更新图表数据
    questionTrendData.value = {
      labels: monthLabels,
      datasets: [
        {
          label: '试题数量',
          data: monthData,
          borderColor: '#409EFF',
          backgroundColor: 'rgba(64, 158, 255, 0.1)',
          fill: true
        }
      ]
    }
  } catch (error) {
    console.error('获取试题增长趋势数据失败:', error)
  }
}

// 加载试题类型分布数据
const loadQuestionTypeData = async () => {
  try {
    // 题型类别
    const questionTypes = [
      {type: 1, name: '单选题'},
      {type: 2, name: '多选题'},
      {type: 3, name: '判断题'},
      {type: 4, name: '解答题'},
      {type: 5, name: '填空题'}
    ]

    const typeLabels = []
    const typeData = []
    const backgroundColor = [
      '#4CAF50', '#2196F3', '#FFC107', '#9C27B0', '#FF5722'
    ]

    // 获取每种题型的数据
    for (const questionType of questionTypes) {
      const res = await questionBankDashboard({
        questionType: questionType.type
      })

      if (res.code === 200) {
        typeLabels.push(questionType.name)
        typeData.push(res.data || 0)
      }
    }

    // 更新图表数据
    questionTypeData.value = {
      labels: typeLabels,
      datasets: [
        {
          data: typeData,
          backgroundColor: backgroundColor.slice(0, typeData.length)
        }
      ]
    }
  } catch (error) {
    console.error('获取试题类型分布数据失败:', error)
  }
}

// 加载试题难度分布数据
const loadQuestionDifficultyData = async () => {
  try {
    // 难度类别
    const difficultyLevels = [
      {level: 1, name: '简单'},
      {level: 2, name: '正常'},
      {level: 3, name: '困难'},
      {level: 4, name: '挑战'}
    ]

    const difficultyLabels = []
    const difficultyData = []
    const backgroundColor = [
      'rgba(103, 194, 58, 0.6)',
      'rgba(230, 162, 60, 0.6)',
      'rgba(245, 108, 108, 0.6)',
      'rgba(144, 147, 153, 0.6)'
    ]

    // 获取每个难度级别的数据
    for (const difficultyLevel of difficultyLevels) {
      const res = await questionBankDashboard({
        difficulty: difficultyLevel.level
      })

      if (res.code === 200) {
        difficultyLabels.push(difficultyLevel.name)
        difficultyData.push(res.data || 0)
      }
    }

    // 更新图表数据
    questionDifficultyData.value = {
      labels: difficultyLabels,
      datasets: [
        {
          label: '试题数量',
          data: difficultyData,
          backgroundColor: backgroundColor.slice(0, difficultyData.length)
        }
      ]
    }
  } catch (error) {
    console.error('获取试题难度分布数据失败:', error)
  }
}

// 加载系统公告数据
const loadSystemNotices = async () => {
  try {
    // 直接请求公告数据
    const res = await listNotice({
      pageNum: 1,
      pageSize: 10 // 获取较多公告以确保有足够数据排序后选择
    })

    if (res.code === 200 && res.rows) {
      // 转换公告数据格式，按创建时间倒序排序，并只保留最新的3条
      systemNotices.value = res.rows
        .map(notice => ({
          title: notice.noticeTitle,
          content: notice.noticeContent,
          time: notice.createTime,
          type: notice.noticeType
        }))
        .sort((a, b) => new Date(b.time) - new Date(a.time)) // 按时间倒序排序
        .slice(0, 3) // 只保留前3条最新公告
    }
  } catch (error) {
    console.error('获取系统公告数据失败:', error)
  }
}

// 页面加载时初始化
onMounted(() => {
  // 调用API获取真实数据
  loadUserDashboardData()
  loadQuestionBankDashboardData()
  loadPaperDashboardData()
  loadKnowledgeTreeDashboardData()
  loadQuestionTrendData() // 加载试题增长趋势数据
  loadQuestionTypeData() // 加载试题类型分布数据
  loadQuestionDifficultyData() // 加载试题难度分布数据
  loadSystemNotices() // 加载系统公告数据
})
</script>

<style scoped>
.dashboard-container {
  padding: 10px;
}

/* 统计卡片样式 */
.statistics-card {
  margin-bottom: 10px;
  border-radius: 8px;
  transition: all 0.3s;
  border: none;
}

.statistics-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
}

.card-icon {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.card-info {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 5px;
}

.card-value {
  font-size: 26px;
  font-weight: 700;
  color: white;
  margin-bottom: 5px;
}

.card-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.user-card {
  background: linear-gradient(135deg, #1890ff, #36cbcb);
}

.question-card {
  background: linear-gradient(135deg, #52c41a, #bde672);
}

.paper-card {
  background: linear-gradient(135deg, #faad14, #ffcd38);
}

.knowledge-card {
  background: linear-gradient(135deg, #eb2f96, #ff85c0);
}

.trend-up {
  color: #52c41a;
  margin-right: 5px;
}

.trend-down {
  color: #f5222d;
  margin-right: 5px;
}

/* 图表卡片样式 */
.chart-row {
  margin-bottom: 2px;
}

.chart-card {
  height: 340px;
  margin-bottom: 20px;
}

.chart-container {
  height: 270px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 表格卡片样式 */
.table-card {
  margin-bottom: 20px;
}

/* 公告样式 */
.notice-card {
  margin-bottom: 2px;
  height: 100%;
}

.notice-list {
  max-height: 350px;
  overflow-y: auto;
}

.notice-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.notice-item:last-child {
  border-bottom: none;
}

.notice-title {
  font-weight: bold;
  margin-bottom: 5px;
  color: #303133;
}

.notice-content {
  font-size: 13px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.notice-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
}
</style>
