<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams">
      <el-form-item label="内容查询" prop="name">
        <el-input
            v-model="queryParams.name"
            clearable
            placeholder="请输入内容名称"
            style="width: 200px"
            @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--            type="primary"-->
      <!--            plain-->
      <!--            icon="Plus"-->
      <!--            @click="handleAdd"-->
      <!--            v-hasPermi="['qh:knowledge:update']"-->
      <!--        >新增-->
      <!--        </el-button>-->
      <!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
            icon="Sort"
            plain
            type="primary"
            @click="toggleExpandAll"
        >展开/折叠
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="knowledgeList"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        row-key="id"
    >
      <el-table-column label="知识结构" prop="name" width="660"></el-table-column>
      <el-table-column label="排序" prop="orderNum" width="200"></el-table-column>
      <el-table-column label="状态" prop="status" width="100">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column align="center" label="创建时间" prop="createTime" width="200">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
        <template #default="scope">
          <el-button v-if="scope.row.parentId !== '0'" v-hasPermi="['qh:knowledge:update']" icon="Edit" link type="primary"
                     @click="handleUpdate(scope.row)">修改
          </el-button>
          <el-button v-hasPermi="['qh:knowledge:update']" icon="Plus" link type="primary" @click="handleAdd(scope.row)">
            新增
          </el-button>
          <el-button v-if="scope.row.parentId !== '0'" v-hasPermi="['qh:knowledge:update']" icon="Delete" link
                     type="primary" @click="handleDelete(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改知识点对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body width="600px">
      <el-form ref="knowledgeRef" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col v-if="form.parentId !== 0" :span="24">
            <el-form-item label="上级内容" prop="parentId">
              <el-tree-select
                  v-model="form.parentId"
                  :data="knowledgeOptions"
                  :props="{ value: 'id', label: 'name', children: 'children' }"
                  check-strictly
                  placeholder="选择上级内容"
                  value-key="id"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内容名称" prop="name" style="width: 400px">
              <el-input v-model="form.name" placeholder="请输入内容名称"/>
            </el-form-item>
            <el-form-item label="显示排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" :min="0" controls-position="right"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">

          </el-col>
          <el-col :span="12">
            <el-form-item label="内容状态" prop="status">
              <el-select v-model="form.status" clearable placeholder="请选择状态" style="width: 100%">
                <el-option
                    v-for="dict in sys_normal_disable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="KnowledgeTreeDetail" setup>
import {
  addKnowledgeTree,
  delKnowledgeTree,
  getKnowledgeTree,
  selectKnowledgeTreeList,
  updateKnowledgeTree
} from "@/api/qh/knowledgeTree.js";

const {proxy} = getCurrentInstance();
const {sys_normal_disable} = proxy.useDict("sys_normal_disable");

const route = useRoute();
const knowledgeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const knowledgeOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);

const data = reactive({
  form: {},
  queryParams: {
    name: undefined,
    ancestors: undefined
  },
  rules: {
    parentId: [{required: true, message: "上级内容不能为空", trigger: "blur"}],
    name: [{required: true, message: "内容名称不能为空", trigger: "blur"}],
    orderNum: [{required: true, message: "显示排序不能为空", trigger: "blur"}],
  },
});

const {queryParams, form, rules} = toRefs(data);

/** 查询内容列表 */
function getList() {
  loading.value = true;
  selectKnowledgeTreeList(queryParams.value).then(response => {
    knowledgeList.value = proxy.handleTree(response.data, "id");
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    parentId: undefined,
    name: undefined,
    orderNum: 0,
    status: "0"
  };
  proxy.resetForm("knowledgeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  selectKnowledgeTreeList(queryParams.value).then(response => {
    knowledgeOptions.value = proxy.handleTree(response.data, "id");
  });
  if (row !== undefined) {
    form.value.parentId = row.id;
  }
  open.value = true;
  title.value = "添加内容";
}

/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  selectKnowledgeTreeList(row.id).then(response => {
    knowledgeOptions.value = proxy.handleTree(response.data, "id");
  });
  getKnowledgeTree(row.id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改内容";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["knowledgeRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateKnowledgeTree(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addKnowledgeTree(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除名称为"' + row.name + '"的数据项? 此操作将会同步删除子节点以及相关的试题信息').then(function () {
    return delKnowledgeTree(row.id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

(() => {
  queryParams.value.ancestors = route.params && route.params.knowledgeTreeId;
  getList();
})();
</script>
