<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="search-card" shadow="hover">
      <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" class="search-form">
        <el-form-item label="题库名称" prop="knowledgeTreeName">
          <el-input
              v-model="queryParams.name"
              clearable
              placeholder="请输入题库名称"
              @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="创建人" prop="createBy">
          <el-input
              v-model="queryParams.createBy"
              clearable
              placeholder="请输入创建人"
              @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工具区域 -->
    <el-card class="toolbar-card" shadow="hover">
      <div class="toolbar-container">
        <div class="left-tools">
          <el-button
              v-hasPermi="['qh:knowledgeTree:update']"
              icon="Plus"
              plain
              type="primary"
              @click="handleAdd"
          >新建题库
          </el-button>
          <el-button
              v-hasPermi="['qh:knowledgeTree:query']"
              icon="Download"
              plain
              type="warning"
              @click="handleExport"
          >导出
          </el-button>
        </div>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card v-loading="loading" class="table-card" shadow="hover">
      <el-table
          :cell-style="{ padding: '8px 0' }"
          :data="knowledgeTreePageList"
          :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
          border
          fit
          highlight-current-row
          row-key="id"
          style="width: 100%"
          @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" fixed="left" type="selection" width="50"/>
        <el-table-column align="left" label="题库名称" min-width="120" prop="name" show-overflow-tooltip/>
        <el-table-column align="center" label="共享属性" min-width="80" prop="shareType">
          <template #default="scope">
            <dict-tag :options="sys_qh_share_type" :value="scope.row.shareType"/>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" min-width="80" prop="status">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.status"/>
          </template>
        </el-table-column>
        <el-table-column align="left" label="备注" min-width="120" prop="remark" show-overflow-tooltip/>
        <el-table-column align="center" label="创建人" min-width="80" prop="createBy"/>
        <el-table-column align="center" label="创建时间" min-width="100" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" fixed="right" label="操作" min-width="120">
          <template #default="scope">
            <div class="operation-buttons">
              <el-tooltip content="修改" placement="top">
                <el-button v-hasPermi="['qh:knowledgeTree:update']" icon="Edit" link type="primary"
                           @click="handleUpdate(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top">
                <el-button v-hasPermi="['qh:knowledgeTree:update']" icon="Delete" link type="primary"
                           @click="handleDelete(scope.row)"></el-button>
              </el-tooltip>
              <el-tooltip content="详情" placement="top">
                <el-button v-hasPermi="['qh:knowledgeTree:query']" icon="View" link type="primary"
                           @click="handleKnowledgeTreeDetail(scope.row)"></el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页控件 -->
      <div class="pagination-container">
        <pagination
            v-show="total>0"
            v-model:limit="queryParams.pageSize"
            v-model:page="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />
      </div>
    </el-card>

    <!-- 添加或修改题库对话框 -->
    <el-dialog
        v-model="open"
        :close-on-click-modal="false"
        :title="title"
        append-to-body
        destroy-on-close
        width="500px"
    >
      <el-form ref="knowledgeTreeRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="题库名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入题库名称"/>
        </el-form-item>
        <el-form-item label="共享属性" prop="shareType">
          <el-select
              v-model="form.shareType"
              clearable
              placeholder="请选择共享属性"
              style="width: 100%"
          >
            <el-option
                v-for="dict in sys_qh_share_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题库状态" prop="status">
          <el-select v-model="form.status" clearable placeholder="请选择状态" style="width: 100%">
            <el-option
                v-for="dict in sys_normal_disable"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="title === '添加题库'" label="数据来源">
          <el-tree-select
              v-model="form.copyFromTopNode"
              :data="knowledgeTreePageList"
              :props="{ value: 'id', label: 'name', children: 'children' }"
              check-strictly
              placeholder="选择数据来源"
              style="width: 100%"
              value-key="knowledgeId"
          />
        </el-form-item>
        <el-form-item label="题库备注" prop="remark">
          <el-input v-model="form.remark" :rows="3" placeholder="请输入备注信息" type="textarea"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="KnowledgeTree" setup>
import {
  addKnowledgeTree,
  delKnowledgeTree,
  getKnowledgeTree,
  selectKnowledgeTreePage,
  updateKnowledgeTree
} from "@/api/qh/knowledgeTree.js";

const {proxy} = getCurrentInstance();
const {sys_qh_share_type, sys_normal_disable} = proxy.useDict("sys_qh_share_type", "sys_normal_disable");

const router = useRouter();
const knowledgeTreePageList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    id: null,
    name: null,
    pageNum: 1,
    pageSize: 10,
    createBy: null,
  },
  rules: {
    name: [{required: true, message: "题库名称不能为空", trigger: "blur"}],
    status: [{required: true, message: "状态不能为空", trigger: "change"}],
    shareType: [{required: true, message: "共享属性不能为空", trigger: "change"}],
    orderNum: [{required: true, message: "显示排序不能为空", trigger: "blur"}],
  },
});

const {queryParams, form, rules} = toRefs(data);

/** 查询题库列表 */
function getList() {
  loading.value = true;
  // 只查询顶层节点信息
  queryParams.value.parentId = '0';
  selectKnowledgeTreePage(queryParams.value).then(res => {
    knowledgeTreePageList.value = res.rows;
    loading.value = false;
    total.value = res.total;
  }).catch(() => {
    loading.value = false;
    proxy.$modal.msgError("获取题库列表失败");
    knowledgeTreePageList.value = [];
    total.value = 0;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    status: '0',
    shareType: null,
    orderNum: 0,
    remark: ''
  };
  proxy.resetForm("knowledgeTreeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.id = undefined;
  queryParams.value.name = undefined;
  queryParams.value.createBy = undefined;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加题库";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value
  getKnowledgeTree(_id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改题库";
  });
}

/** 跳转结构详情分配 */
function handleKnowledgeTreeDetail(row) {
  const knowledgeTreeId = row.id;
  router.push("/qh/knowledgeTree-detail/index/" + knowledgeTreeId);
};

/** 提交按钮 */
function submitForm() {
  // 题库的parentId设置为0，表示树的顶层节点
  form.value.parentId = '0';
  // 排序默认有限展示共享题库、个人题库，再按照orderNum排序
  form.value.orderNum = 0;
  proxy.$refs["knowledgeTreeRef"].validate(valid => {
    if (valid) {
      if (form.value.id != null) {
        updateKnowledgeTree(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addKnowledgeTree(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除选中的题库? 此操作将会同步删除题库中的试题信息').then(function () {
    return delKnowledgeTree(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  // 导出时导出所有节点数据
  proxy.download('qh/knowledgeTree/export', {
    ...queryParams.value
  }, `题库数据_${new Date().getTime()}.xlsx`)
}

getList();
</script>

<style scoped>
.app-container {
  padding: 15px;
  background-color: #f5f7fa;
}

.search-card, .toolbar-card, .table-card {
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-tools {
  display: flex;
  gap: 10px;
}

.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.pagination-container {
  margin-top: 2px;
  display: flex;
  justify-content: flex-end;
}

@media screen and (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .search-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }

  .toolbar-container {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .left-tools {
    width: 100%;
    justify-content: space-between;
  }

  .pagination-container {
    justify-content: center;
  }
}

@media screen and (max-width: 480px) {
  .left-tools {
    flex-direction: column;
  }

  .left-tools .el-button {
    width: 100%;
  }
}
</style>
