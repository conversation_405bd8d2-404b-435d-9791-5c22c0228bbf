<template>
  <div class="app-container">
    <el-row :gutter="24">
      <!--内容树状数据-->
      <el-col :span="5" :xs="24" style="padding: 0">
        <div v-loading="treeLoading"
             element-loading-text="正在加载知识树数据..."
             element-loading-background="rgba(255, 255, 255, 0.9)"
             class="head-container">
          <!-- 添加下拉框和输入框 -->
          <div class="tree-search-container">
            <el-select
                v-model="queryParams.topNode"
                class="library-select"
                clearable
                placeholder="请选择题库"
                @change="handleTreeSelectChange"
            >
              <el-option
                  v-for="item in knowledgeTreeSelectOptions"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
              />
            </el-select>

            <!-- 新增层级筛选下拉框 -->
            <el-select
                v-model="levelFilter"
                class="level-select"
                placeholder="请选择层级"
                @change="applyLevelFilter"
            >
              <el-option label="默认展示" value="all" />
              <el-option label="章节信息展示" value="2-3" />
              <el-option label="章节与知识点" value="3-4" />
              <el-option label="知识点展示" value="4" />
            </el-select>

            <el-input
                v-model="filterText"
                class="filter-input"
                clearable
                placeholder="搜索知识点"
                prefix-icon="Search"
            />
          </div>

          <div class="tree-container">
            <el-empty v-if="!treeLoading && !queryParams.topNode" description="请先选择题库"></el-empty>
            <el-tree
                v-else-if="!treeLoading && filteredTreeData.length > 0"
                ref="knowledgeTreeRef"
                :data="filteredTreeData"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                :props="{ label: 'name', children: 'children' }"
                class="knowledge-tree"
                highlight-current
                node-key="id"
                @node-click="handleNodeClick"
            >
              <template #default="{ node, data }">
                <span class="tree-node-content">
                  <span class="node-label">{{ node.label }}</span>
                </span>
              </template>
            </el-tree>
            <el-empty v-else-if="!treeLoading && filteredTreeData.length === 0 && queryParams.topNode"
                      description="暂无知识点数据"></el-empty>
          </div>
        </div>
      </el-col>
      <el-col :span="19" :xs="24">
        <!--试题查询-->
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
          <el-form-item label="难度选择">
            <el-select v-model="queryParams.difficultyList" clearable multiple placeholder="难度选择"
                       style="width: 200px">
              <el-option v-for="dict in sys_qh_difficulty" :key="dict.value" :label="dict.label" :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="题型选择">
            <el-select v-model="queryParams.questionTypeList" clearable multiple placeholder="题型选择"
                       style="width: 200px">
              <el-option v-for="dict in sys_qh_questions_type" :key="dict.value" :label="dict.label"
                         :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="试卷类型">
            <el-select v-model="queryParams.paperTypeList" clearable multiple placeholder="卷型选择"
                       style="width: 200px">
              <el-option v-for="dict in sys_qh_paper_type" :key="dict.value" :label="dict.label"
                         :value="dict.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="试卷来源">
            <el-input v-model="queryParams.sourcePaper" clearable placeholder="请输入试卷来源"
                      style="width: 200px"
                      @keyup.enter="handleQuery"/>
          </el-form-item>
        </el-form>
        <!--试题查询-->
        <el-form v-show="showSearch" ref="queryRef" :inline="true" :model="queryParams" label-width="68px">
          <el-form-item label="试题年份" style="width: 270px">
            <el-date-picker
                v-model="queryParams.yearList"
                range-separator="-"
                type="years"
                value-format="YYYY"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="试题地区">
            <el-cascader
                v-model="queryParams.regionList"
                :options="regionData"
                :props="{ value: 'value', label: 'label', children: 'children' }"
                clearable
                placeholder="请输入地区"
                class="info-select"
                style="width: 200px"
                @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="试题标签">
            <el-input v-model="queryParams.tag" clearable placeholder="请输入标签"
                      style="width: 200px"
                      @keyup.enter="handleQuery"/>
          </el-form-item>
          <el-form-item label="关键词">
            <el-input v-model="queryParams.keyword" clearable placeholder="请输入关键词"
                      style="width: 200px"
                      @keyup.enter="handleQuery"/>
          </el-form-item>
          <el-form-item>
            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button v-hasPermi="['qh:questionBank:add']" icon="Plus" type="success" @click="handleAddQuestion">
              试题录入
            </el-button>
            <el-button
                v-hasPermi="['qh:questionBank:export']"
                icon="Download"
                type="primary"
                :disabled="selectedQuestions.length === 0"
                @click="openExportDialog(selectedQuestions)"
            >
              导出
            </el-button>
          </el-form-item>
        </el-form>
        <div v-loading="loading"
             element-loading-text="正在加载试题数据..."
             element-loading-background="rgba(255, 255, 255, 0.9)"
             class="scroll-container">
          <!-- 试题展示 -->
          <div class="question-list" style="border:none">
            <div v-if="!loading && questionBankList.length === 0" class="empty-question-container">
              <el-empty description="暂无试题">
                <template #description>
                  <p class="empty-description">暂无试题内容，快去给题库添加试题吧！</p>
                </template>
              </el-empty>
            </div>
            <transition-group v-if="!loading && questionBankList.length > 0" name="list">
              <div
                  v-for="(row, index) in questionBankList"
                  :key="row.id"
                  class="question-item"
              >
                <div class="cell">
                  <!-- 在template部分修改meta-info结构 -->
                  <div class="meta-info" style="padding-left: 10px; padding-right: 4px">
                    <span>{{ index + 1 }}.</span>
                    <!--                    &lt;!&ndash; 添加选择框 &ndash;&gt;-->
                    <!--                    <div class="selection-control">-->

                    <!--                    </div>-->
                    <el-checkbox v-model="row.selected" @change="onSelectChange(row)"/>
                    <div class="tags-container">
                      <dict-tag
                          :options="sys_qh_questions_type"
                          :value="row.questionType"
                          class="type-tag unified-tag"
                      />
                      <dict-tag
                          :options="sys_qh_difficulty"
                          :value="row.difficulty"
                          class="difficulty-tag unified-tag"
                      />
                    </div>
                    <!-- 知识点: -->
                    <div
                        v-if="row.knowledgeTreeList?.length > 0"
                        class="knowledge-box"
                    >
                      <span class="meta-info-label">知识点: </span>
                      <div class="knowledge-list">
                      <span
                          v-for="(item, idx) in row.knowledgeTreeList"
                          :key="idx"
                          class="knowledge-item"
                      >
                        {{ item?.name }}
                        <span
                            v-if="idx < row.knowledgeTreeList.length - 1"
                            class="separator"
                        >/</span>
                      </span>
                      </div>
                    </div>
                    <div class="button-group">
                      <!-- 添加上传答案按钮 -->
                      <el-button
                          v-hasPermi="['qh:questionBank:update']"
                          class="question-button"
                          icon="Edit"
                          plain
                          type="warning"
                          @click="handleUploadAnswer(row)"
                      >
                        上传答案
                      </el-button>
                      <!-- 上传解析按钮 -->
                      <el-button
                          v-hasPermi="['qh:questionBank:update']"
                          class="question-button"
                          icon="Edit"
                          plain
                          type="success"
                          @click="handleUploadAnalysis(row)"
                      >
                        上传解析
                      </el-button>
                      <!--                      <el-button-->
                      <!--                          v-if="!row.inExport"-->
                      <!--                          v-hasPermi="['qh:questionBank:query']"-->
                      <!--                          class="question-button"-->
                      <!--                          icon="Download"-->
                      <!--                          plain-->
                      <!--                          type="warning"-->
                      <!--                          @click="addToQuestionBoard('export',row, $event)"-->
                      <!--                      >-->
                      <!--                        导出栏-->
                      <!--                      </el-button>-->
                      <!--                      <el-button-->
                      <!--                          v-else-->
                      <!--                          v-hasPermi="['qh:questionBank:update']"-->
                      <!--                          class="question-button"-->
                      <!--                          icon="Remove"-->
                      <!--                          plain-->
                      <!--                          type="danger"-->
                      <!--                          @click="removeFromQuestionBoard('export',row.id)"-->
                      <!--                      >-->
                      <!--                        取消-->
                      <!--                      </el-button>-->

                      <el-button
                          v-if="!row.inBoard"
                          v-hasPermi="['qh:questionBank:update']"
                          class="question-button"
                          icon="Plus"
                          plain
                          type="primary"
                          @click="addToQuestionBoard('board',row, $event)"
                      >
                        试题栏
                      </el-button>
                      <el-button
                          v-else
                          v-hasPermi="['qh:questionBank:update']"
                          class="question-button"
                          icon="Remove"
                          plain
                          type="danger"
                          @click="removeFromQuestionBoard('board',row.id)"
                      >
                        取消
                      </el-button>
                    </div>
                  </div>

                  <!-- 题干图片 -->
                  <div class="context-image">
                    <div class="index-image-wrapper">
                      <el-image
                          :hide-on-click-modal="true"
                          :src="row.context"
                          fit="contain"
                          oncontextmenu="return false;"
                      >
                        <template #error>
                          <div class="image-error">图片加载失败</div>
                        </template>
                      </el-image>
                    </div>
                  </div>
                  <div class="answer-control">
                    <!-- 试题来源试卷信息 -->
                    <span v-if="row.sourcePaper || row.year || row.region || row.tag" class="question-source">
                      <span v-if="row.sourcePaper" class="source-info-item">来源：{{
                          formatTitle(row.sourcePaper)
                        }}</span>
                      <span v-if="row.year" class="source-info-item">年份：{{ row.year }}</span>
                      <span v-if="row.region" class="source-info-item">地区：{{ formatRegion(row.region) }}</span>
                      <span v-if="row.tag" class="source-tag-item">标签：{{ row.tag }}</span>
                    </span>

                    <!-- 添加答案控制按钮 -->
                    <el-button
                        link
                        style="margin-left: auto; text-align: right"
                        type="success"
                        @click="toggleAnswer(row, 'answer')"
                    >
                      <el-icon>
                        <Hide v-if="row.showAnswer"/>
                        <Connection v-else/>
                      </el-icon>
                      {{ row.showAnswer ? '隐藏答案' : '展开答案' }}
                    </el-button>

                    <!-- 保留原有解析按钮 -->
                    <el-button
                        v-if="row.questionAnalyze"
                        link
                        style="margin-left: 10px; text-align: right"
                        type="primary"
                        @click="toggleAnswer(row, 'analysis')"
                    >
                      <el-icon>
                        <Hide v-if="row.showAnalysis"/>
                        <Connection v-else/>
                      </el-icon>
                      {{ row.showAnalysis ? '隐藏解析' : '展开解析' }}
                    </el-button>
                  </div>
                  <!-- 添加答案图片展示区域 -->
                  <div>
                    <transition name="custom-zoom">
                      <div v-show="row.showAnswer" class="context-image">
                        <el-image
                            :hide-on-click-modal="true"
                            :src="row.questionAnswer || 'http://117.72.14.183:9000/qh-test/答案默认图片.png'"
                            draggable="false"
                            fit="contain"
                            oncontextmenu="return false;"
                        >
                          <template #error>
                            <div class="image-error">答案图片加载失败</div>
                          </template>
                        </el-image>
                      </div>
                    </transition>
                  </div>

                  <!-- 保留原有解析图片展示 -->
                  <div>
                    <transition name="custom-zoom">
                      <div v-show="row.showAnalysis" class="context-image">
                        <el-image
                            :hide-on-click-modal="true"
                            :src="row.questionAnalyze || 'http://117.72.14.183:9000/qh-test/解析默认图片.png'"
                            draggable="false"
                            fit="contain"
                            oncontextmenu="return false;"
                        >
                          <template #error>
                            <div class="image-error">解析图片加载失败</div>
                          </template>
                        </el-image>
                      </div>
                    </transition>
                  </div>
                </div>
              </div>
            </transition-group>
          </div>
        </div>

        <pagination v-show="total > 0" v-model:limit="queryParams.pageSize" v-model:page="queryParams.pageNum"
                    :total="total"
                    style="text-align: center; margin-top: 0px" @pagination="getList"/>
      </el-col>
    </el-row>

    <!-- 试题导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" append-to-body width="400px">
      <el-upload ref="uploadRef" :action="upload.url + '?updateSupport=' + upload.updateSupport" :auto-upload="false"
                 :disabled="upload.isUploading"
                 :headers="upload.headers" :limit="1"
                 :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" accept=".xlsx, .xls" drag>
        <el-icon class="el-icon--upload">
          <upload-filled/>
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport"/>
              是否更新已经存在的试题数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link :underline="false" style="font-size:12px;vertical-align: baseline;" type="primary"
                     @click="importTemplate">下载模板
            </el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!--    &lt;!&ndash; 固定定位的导出栏按钮 &ndash;&gt;-->
    <!--    <div-->
    <!--        class="export-floating-button export-drawer-button"-->
    <!--        @click="openExportDrawer"-->
    <!--    >-->
    <!--      <el-button circle class="button-icon" type="warning">-->
    <!--        <el-icon>-->
    <!--          <Download/>-->
    <!--        </el-icon>-->
    <!--        <span v-if="exportCount > 0" class="badge">{{ exportCount }}</span>-->
    <!--      </el-button>-->
    <!--      <div class="button-text">导出栏</div>-->
    <!--    </div>-->

    <!-- 导出栏抽屉 -->
    <!--    <el-drawer-->
    <!--        v-model="exportDrawerVisible"-->
    <!--        :append-to-body="true"-->
    <!--        :with-header="false"-->
    <!--        class="export-drawer"-->
    <!--        direction="rtl"-->
    <!--        size="40%"-->
    <!--    >-->
    <!--      <div class="drawer-header">-->
    <!--        <span class="drawer-title">导出栏（{{ exportQuestions.length }}）</span>-->
    <!--        <div class="drawer-actions">-->
    <!--          <el-button :disabled="exportQuestions.length === 0" icon="Delete" size="small" type="danger"-->
    <!--                     @click="handleClearBoard('export')">清空-->
    <!--          </el-button>-->
    <!--          <el-button :disabled="exportQuestions.length === 0" icon="Document" size="small" type="primary"-->
    <!--                     @click="handleExport">导出-->
    <!--          </el-button>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <el-scrollbar height="calc(100% - 60px)">-->
    <!--        <div v-if="exportQuestions.length === 0" class="empty-tip">-->
    <!--          <el-empty description="暂无试题"/>-->
    <!--        </div>-->
    <!--        <draggable-->
    <!--            v-model="exportQuestions"-->
    <!--            :animation="150"-->
    <!--            :delay="50"-->
    <!--            :disabled="exportQuestions.length <= 1"-->
    <!--            class="draggable-container"-->
    <!--            item-key="id"-->
    <!--            @start="onDragStart"-->
    <!--            @end="onDragEnd"-->
    <!--        >-->
    <!--          <template #item="{element, index}">-->
    <!--            <div-->
    <!--                :key="element.id"-->
    <!--                class="selected-question"-->
    <!--            >-->
    <!--              <div class="question-header">-->
    <!--                <span class="drag-icon" title="拖动调整顺序"><el-icon><Operation/></el-icon></span>-->
    <!--                <span class="question-index">{{ index + 1 }}.</span>-->
    <!--                <div class="question-meta">-->
    <!--                  <dict-tag-->
    <!--                      :options="sys_qh_questions_type"-->
    <!--                      :value="element.questionType"-->
    <!--                      class="type-tag"-->
    <!--                  />-->
    <!--                  <dict-tag-->
    <!--                      :options="sys_qh_difficulty"-->
    <!--                      :value="element.difficulty"-->
    <!--                      class="difficulty-tag"-->
    <!--                  />-->
    <!--                  <span-->
    <!--                      v-for="(data, idx) in element.knowledgeTreeList"-->
    <!--                      :key="idx"-->
    <!--                      class="knowledge-item"-->
    <!--                  >-->
    <!--                        {{ data?.name }}-->
    <!--                        <span-->
    <!--                            v-if="idx < element.knowledgeTreeList.length - 1"-->
    <!--                            class="separator"-->
    <!--                        >/</span>-->
    <!--                      </span>-->
    <!--                </div>-->
    <!--                <el-button-->
    <!--                    circle-->
    <!--                    icon="Delete"-->
    <!--                    size="small"-->
    <!--                    type="danger"-->
    <!--                    @click="removeFromQuestionBoard('export',element.id)"-->
    <!--                />-->
    <!--              </div>-->
    <!--              <el-image-->
    <!--                  :src="element.context"-->
    <!--                  class="question-image"-->
    <!--                  fit="contain"-->
    <!--              >-->
    <!--                <template #error>-->
    <!--                  <div class="image-error">图片加载失败</div>-->
    <!--                </template>-->
    <!--              </el-image>-->
    <!--            </div>-->
    <!--          </template>-->
    <!--        </draggable>-->
    <!--      </el-scrollbar>-->
    <!--    </el-drawer>-->

    <!-- 固定定位的试题栏按钮 -->
    <div
        class="floating-button question-drawer-button"
        @click="openQuestionDrawer"
    >
      <el-button circle class="button-icon" type="primary">
        <el-icon>
          <FolderOpened/>
        </el-icon>
        <span v-if="boardCount > 0" class="badge">{{ boardCount }}</span>
      </el-button>
      <div class="button-text">试题栏</div>
    </div>

    <!-- 试题栏抽屉 -->
    <el-drawer
        v-model="drawerVisible"
        :append-to-body="true"
        :with-header="false"
        class="question-drawer"
        direction="rtl"
        size="40%"
    >
      <div class="drawer-header">
        <span class="drawer-title">试题栏（{{ boardQuestions.length }}）</span>
        <div class="drawer-actions">
          <el-button :disabled="boardQuestions.length === 0" icon="Delete" size="small" type="danger"
                     @click="handleClearBoard('board')">清空
          </el-button>
          <el-button :disabled="boardQuestions.length === 0" icon="Document" size="small" type="primary"
                     @click="handleManualPaper">手动组卷
          </el-button>
          <el-button :disabled="boardQuestions.length === 0" icon="Download" size="small" type="success"
                     @click="openExportDialog(boardQuestions)">导出
          </el-button>
        </div>
      </div>
      <el-scrollbar height="calc(100% - 60px)">
        <div v-if="boardQuestions.length === 0" class="empty-tip">
          <el-empty description="暂无试题"/>
        </div>
        <draggable
            v-model="boardQuestions"
            :animation="150"
            :delay="50"
            :disabled="boardQuestions.length <= 1"
            class="draggable-container"
            item-key="id"
            @start="onDragStart"
            @end="onDragEnd"
        >
          <template #item="{element, index}">
            <div
                :key="element.id"
                class="selected-question"
            >
              <div class="question-header">
                <span class="drag-icon" title="拖动调整顺序"><el-icon><Operation/></el-icon></span>
                <span class="question-index">{{ index + 1 }}.</span>
                <div class="question-meta">
                  <dict-tag
                      :options="sys_qh_questions_type"
                      :value="element.questionType"
                      class="type-tag"
                  />
                  <dict-tag
                      :options="sys_qh_difficulty"
                      :value="element.difficulty"
                      class="difficulty-tag"
                  />
                  <span
                      v-for="(data, idx) in element.knowledgeTreeList"
                      :key="idx"
                      class="knowledge-item"
                  >
                        {{ data?.name }}
                        <span
                            v-if="idx < element.knowledgeTreeList.length - 1"
                            class="separator"
                        >/</span>
                      </span>
                </div>
                <el-button
                    circle
                    icon="Delete"
                    size="small"
                    type="danger"
                    @click="removeFromQuestionBoard('board',element.id)"
                />
              </div>
              <el-image
                  :src="element.context"
                  class="question-image"
                  fit="contain"
              >
                <template #error>
                  <div class="image-error">图片加载失败</div>
                </template>
              </el-image>
            </div>
          </template>
        </draggable>
      </el-scrollbar>
    </el-drawer>

    <!-- 上传答案对话框 -->
    <el-dialog
        v-model="uploadAnswerVisible"
        append-to-body
        destroy-on-close
        title="上传答案"
        width="500px"
    >
      <div class="upload-analysis-container">
        <el-alert
            v-if="imageError"
            :title="imageError"
            class="mb-3"
            show-icon
            type="error"
        />
        <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :show-file-list="true"
            accept="image/*"
            action="#"
            class="analysis-uploader"
        >
          <template v-if="!uploadFile">
            <div class="upload-area">
              <el-icon class="upload-icon">
                <Plus/>
              </el-icon>
              <div class="upload-text">点击上传答案图片</div>
              <!--              <div class="upload-tip">图片大小限制: 宽度≤1200px, 高度≤300px</div>-->
            </div>
          </template>
          <template v-else>
            <img v-if="previewImage" :src="previewImage" class="preview-image"/>
          </template>
        </el-upload>
        <div v-if="uploadFile" class="preview-actions">
          <el-button
              :loading="uploadLoading"
              type="primary"
              @click="submitUploadAnswer"
          >确认上传
          </el-button>
          <el-button @click="cancelUpload">取消</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 上传解析对话框 -->
    <el-dialog
        v-model="uploadAnalysisVisible"
        append-to-body
        destroy-on-close
        title="上传解析"
        width="500px"
    >
      <div class="upload-analysis-container">
        <el-alert
            v-if="imageError"
            :title="imageError"
            class="mb-3"
            show-icon
            type="error"
        />
        <el-upload
            ref="uploadRef"
            :auto-upload="false"
            :limit="1"
            :on-change="handleFileChange"
            :show-file-list="true"
            accept="image/*"
            action="#"
            class="analysis-uploader"
        >
          <template v-if="!uploadFile">
            <div class="upload-area">
              <el-icon class="upload-icon">
                <Plus/>
              </el-icon>
              <div class="upload-text">点击上传解析图片</div>
              <!--              <div class="upload-tip">图片大小限制: 宽度≤1200px, 高度≤300px</div>-->
            </div>
          </template>
          <template v-else>
            <img v-if="previewImage" :src="previewImage" class="preview-image"/>
          </template>
        </el-upload>
        <div v-if="uploadFile" class="preview-actions">
          <el-button
              :loading="uploadLoading"
              type="primary"
              @click="submitUploadAnalysis"
          >确认上传
          </el-button>
          <el-button @click="cancelUpload">取消</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 导出方式选择对话框 -->
    <el-dialog
        v-model="exportDialogVisible"
        title="导出设置"
        width="580px"
        append-to-body
        class="export-dialog"
    >
      <div class="dialog-content">
        <el-form ref="exportFormRef" :model="exportForm" label-width="120px">
          <!-- 基本选项 -->
          <div class="form-section">
            <el-form-item label="包含答案" class="export-form-item">
              <el-switch v-model="exportForm.includeAnswer" active-color="#13ce66" />
            </el-form-item>

            <el-form-item label="包含解析" class="export-form-item">
              <el-switch v-model="exportForm.includeAnalysis" active-color="#13ce66" />
            </el-form-item>
          </div>

          <!-- 导出选项 -->
          <div v-if="exportForm.includeAnswer || exportForm.includeAnalysis" class="form-section">
            <div class="section-title">
              <el-icon><Setting /></el-icon>
              <span>导出选项</span>
            </div>

            <el-form-item label="导出方式" class="export-form-item">
              <el-radio-group v-model="exportForm.mergeType" class="radio-group">
                <el-radio label="merge" border>
                  <el-icon><Connection /></el-icon>
                  合并导出
                </el-radio>
                <el-radio label="separate" border>
                  <el-icon><Sort /></el-icon>
                  分开导出
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <div v-if="exportForm.mergeType === 'merge'">
              <el-form-item label="合并方式" class="export-form-item">
                <el-radio-group v-model="exportForm.layoutType" class="radio-group">
                  <el-radio label="grouped" border>
                    <el-icon><Grid /></el-icon>
                    按题分组
                  </el-radio>
                  <el-radio label="sequential" border>
                    <el-icon><Sort /></el-icon>
                    先题目后答案
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExport">
            <el-icon><Download /></el-icon>
            确定导出
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="questionBank" setup>
import {getToken} from "@/utils/auth";
import {
  addQuestionBankBoard,
  delQuestionBank,
  delQuestionBankBoard,
  listQuestionBank,
  listQuestionBankBoard,
  updateQuestionBank,
  updateQuestionBankBoardOrder
} from "@/api/qh/questionBank.js";
// 在脚本部分导入需要的图标
import {selectKnowledgeTreeList} from "@/api/qh/knowledgeTree.js";
import {onMounted, reactive, ref, toRefs, watch} from 'vue';
import {Connection, FolderOpened, Hide, Operation, Plus,Setting, Sort, Grid, Download } from "@element-plus/icons-vue";
import {useDebounceFn} from '@vueuse/core';
import {fileUpload} from "@/api/qh/minio.js";
import draggable from 'vuedraggable';
import {codeToText, regionData} from "element-china-area-data";
// 其他导入保持不变
import jsPDF from 'jspdf';
import {saveAs} from 'file-saver';
import {Document, ImageRun, Packer, Paragraph} from 'docx';

const router = useRouter();
const {proxy} = getCurrentInstance();
const {
  sys_qh_difficulty,
  sys_qh_questions_type,
  sys_qh_paper_type
} = proxy.useDict("sys_qh_difficulty", "sys_qh_questions_type", "sys_qh_paper_type");
// 页面状态
const questionBankList = ref([]);
const loading = ref(true);
const treeLoading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const dateRange = ref([]);
const knowledgeTreeOptions = ref([]);
const knowledgeTreeSelectOptions = ref([]);
const currentTreeData = ref([]);
const filterText = ref('');
const drawerVisible = ref(false);
const boardQuestions = ref([]);
const boardCount = ref(0);
const exportDrawerVisible = ref(false);
const exportQuestions = ref([]);
const exportCount = ref(0);
const currentDrawerType = ref(''); // 'board' 或 'export'
// 新增响应式变量
const selectedQuestions = ref([]); // 存储选中的试题
// 在data部分添加响应式变量
const levelFilter = ref('all'); // 层级筛选条件
const fullTreeCache = ref({}); // 缓存完整树数据
const filteredTreeData = ref([]); // 存储过滤后的树数据

// 上传答案相关
const uploadAnswerVisible = ref(false);
// 上传解析相关
const uploadAnalysisVisible = ref(false);
const currentQuestion = ref(null);
const previewImage = ref('');
const uploadRef = ref(null);
const uploadLoading = ref(false);
const imageError = ref('');
const uploadFile = ref(null);
// 添加拖拽相关状态
const isDropping = ref(false);

// 记录拖拽前的顺序
const originalBoardQuestions = ref([]);

/*** 试题导入参数 */
const upload = reactive({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: {Authorization: "Bearer " + getToken()},
  url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
});

// 添加导出相关的响应式变量
const exportDialogVisible = ref(false);
const exportForm = reactive({
  includeAnswer: true,
  includeAnalysis: true,
  mergeType: 'merge',
  layoutType: 'grouped'
});

/** 处理树选择变化 */
function handleTreeSelectChange(val) {
  if (!val) {
    filteredTreeData.value = [];
    queryParams.value.knowledgeTreeIds = [];
    return;
  }

  treeLoading.value = true;

  // 检查缓存中是否有该树的完整数据
  if (fullTreeCache.value[val]) {
    applyLevelFilter();
    treeLoading.value = false;
    getList();
    return;
  }

  // 获取选中节点的完整子树结构
  selectKnowledgeTreeList({ancestors: val}).then(response => {
    try {
      // 处理树状结构
      const fullTreeData = proxy.handleTree(response.data, "id");
      fullTreeCache.value[val] = fullTreeData; // 缓存完整树数据

      // 找到选中的顶层节点
      const selectedNode = fullTreeData.find(node => node.id === val);
      if (selectedNode && selectedNode.children) {
        // 应用层级筛选
        applyLevelFilter(selectedNode.children);
      } else {
        filteredTreeData.value = [];
      }

      treeLoading.value = false;
      getList();
    } catch (error) {
      console.error('处理知识树数据失败:', error);
      treeLoading.value = false;
      getList();
    }
  }).catch(() => {
    filteredTreeData.value = [];
    treeLoading.value = false;
    getList();
  });
}

/** 应用层级筛选 */
function applyLevelFilter() {
  if (!queryParams.value.topNode) return;

  const fullTree = fullTreeCache.value[queryParams.value.topNode];
  if (!fullTree) return;

  // 找到选中的顶层节点
  const selectedNode = fullTree.find(node => node.id === queryParams.value.topNode);
  if (!selectedNode) {
    filteredTreeData.value = [];
    return;
  }

  // 根据筛选条件处理树数据
  switch (levelFilter.value) {
    case 'all':
      // 显示整个子树（从第二级开始）
      filteredTreeData.value = selectedNode.children ? [...selectedNode.children] : [];
      break;

    case '2-3':
      // 显示第二级和第三级
      filteredTreeData.value = (selectedNode.children || []).map(level2Node => ({
        ...level2Node,
        children: level2Node.children
            ? level2Node.children.map(level3Node => ({
              ...level3Node,
              children: [] // 清除第四级子节点
            }))
            : []
      }));
      break;

    case '3-4':
      // 显示第三级和第四级
      const level3Nodes = [];
      (selectedNode.children || []).forEach(level2Node => {
        (level2Node.children || []).forEach(level3Node => {
          level3Nodes.push({
            ...level3Node,
            children: level3Node.children || []
          });
        });
      });
      filteredTreeData.value = level3Nodes;
      break;

    case '4':
      // 显示第四级及以下所有节点（修改部分）
      const level4AndBelowNodes = [];
      (selectedNode.children || []).forEach(level2Node => {
        (level2Node.children || []).forEach(level3Node => {
          (level3Node.children || []).forEach(level4Node => {
            // 保留完整的子树结构（包括所有子节点）
            level4AndBelowNodes.push(level4Node);
          });
        });
      });
      filteredTreeData.value = level4AndBelowNodes;
      break;

    default:
      filteredTreeData.value = selectedNode.children ? [...selectedNode.children] : [];
  }
}

// 监听层级筛选变化
watch(levelFilter, () => {
  applyLevelFilter();
});

const handlePageExport = () => {
  if (selectedQuestions.value.length === 0) {
    proxy.$message.warning('请先选择要导出的试题');
    return;
  }

  // 将选中的试题设置为导出内容
  exportQuestions.value = [...selectedQuestions.value];
  exportDialogVisible.value = true;
};


// 统一打开导出对话框的方法
const openExportDialog = (questions) => {
  if (questions.length === 0) {
    proxy.$message.warning('没有可导出的试题');
    return;
  }

  exportQuestions.value = [...questions];
  exportDialogVisible.value = true;
};

// 新增方法 - 处理选择变化
const onSelectChange = (row) => {
  if (row.selected) {
    // 添加选中试题
    if (!selectedQuestions.value.some(q => q.id === row.id)) {
      selectedQuestions.value.push(row);
    }
  } else {
    // 移除取消选中的试题
    const index = selectedQuestions.value.findIndex(q => q.id === row.id);
    if (index !== -1) {
      selectedQuestions.value.splice(index, 1);
    }
  }
};

// 打开导出对话框
const handleExport = () => {
  // 重置表单
  exportForm.format = 'pdf';
  exportForm.includeAnswer = false;
  exportForm.includeAnalysis = false;
  exportForm.mergeType = 'merge';
  exportForm.layoutType = 'grouped';

  exportDialogVisible.value = true;
};

// 确认导出
const confirmExport = async () => {
  exportDialogVisible.value = false;
  await exportToPDF(); // 直接调用PDF导出
};

// 导出为PDF
const exportToPDF = async () => {
  // proxy.$modal.msgLoading('正在生成PDF，请稍候...');

  try {
    const pdf = new jsPDF('p', 'mm', 'a4');
    let position = 0;

    // 仅导出题目
    if (!exportForm.includeAnswer && !exportForm.includeAnalysis) {
      for (let i = 0; i < exportQuestions.value.length; i++) {
        const question = exportQuestions.value[i];
        position = await addQuestionToPDF(pdf, question, i + 1, position);
      }
    }
    // 分开导出
    else if (exportForm.mergeType === 'separate') {
      // 题目部分
      for (let i = 0; i < exportQuestions.value.length; i++) {
        const question = exportQuestions.value[i];
        position = await addQuestionToPDF(pdf, question, i + 1, position);
      }

      // 添加新页面
      pdf.addPage();
      position = 0;

      // 答案和解析部分
      for (let i = 0; i < exportQuestions.value.length; i++) {
        const question = exportQuestions.value[i];
        position = await addAnswerToPDF(pdf, question, i + 1, position);
      }
    }
    // 合并导出
    else {
      if (exportForm.layoutType === 'grouped') {
        // 按题分组：题目+答案+解析
        for (let i = 0; i < exportQuestions.value.length; i++) {
          const question = exportQuestions.value[i];
          position = await addQuestionToPDF(pdf, question, i + 1, position);

          if (exportForm.includeAnswer || exportForm.includeAnalysis) {
            position = await addAnswerToPDF(pdf, question, i + 1, position);
          }

          // 添加分页符（除了最后一个）
          if (i < exportQuestions.value.length - 1) {
            pdf.addPage();
            position = 0;
          }
        }
      } else {
        // 先题目后答案
        // 题目部分
        for (let i = 0; i < exportQuestions.value.length; i++) {
          const question = exportQuestions.value[i];
          position = await addQuestionToPDF(pdf, question, i + 1, position);
        }

        // 添加新页面
        pdf.addPage();
        position = 0;

        // 答案和解析部分
        for (let i = 0; i < exportQuestions.value.length; i++) {
          const question = exportQuestions.value[i];
          position = await addAnswerToPDF(pdf, question, i + 1, position);
        }
      }
    }

    // 保存PDF
    const timestamp = new Date().getTime();
    pdf.save(`试题导出_${timestamp}.pdf`);
    proxy.$modal.msgSuccess('PDF导出成功');
  } catch (error) {
    console.error('导出PDF失败:', error);
    proxy.$modal.msgError('导出PDF失败，请重试');
  } finally {
    proxy.$modal.msgClose();
  }
};

// 添加题目到PDF
const addQuestionToPDF = async (pdf, question, index, position) => {
  // 添加题目序号
  pdf.setFontSize(12);
  pdf.text(`${index}.`, 15, position + 15);

  // 添加题目图片
  const img = await loadImage(question.context);
  const imgWidth = 180; // 图片宽度
  const imgHeight = (img.height * imgWidth) / img.width;

  // 检查是否需要新页面
  if (position + imgHeight > 280) {
    pdf.addPage();
    position = 0;
  }

  pdf.addImage(img, 'JPEG', 20, position + 20, imgWidth, imgHeight);
  return position + imgHeight + 30;
};

// 添加答案到PDF
const addAnswerToPDF = async (pdf, question, index, position) => {
  // 添加答案标题
  pdf.setFontSize(12);
  pdf.text(`题目 ${index} 答案与解析:`, 15, position + 15);

  let currentPosition = position + 25;

  // 添加答案图片
  if (exportForm.includeAnswer && question.questionAnswer) {
    const answerImg = await loadImage(question.questionAnswer);
    const imgWidth = 180;
    const imgHeight = (answerImg.height * imgWidth) / answerImg.width;

    if (currentPosition + imgHeight > 280) {
      pdf.addPage();
      currentPosition = 0;
    }

    pdf.addImage(answerImg, 'JPEG', 20, currentPosition + 20, imgWidth, imgHeight);
    currentPosition += imgHeight + 15;
  }

  // 添加解析图片
  if (exportForm.includeAnalysis && question.questionAnalyze) {
    const analysisImg = await loadImage(question.questionAnalyze);
    const imgWidth = 180;
    const imgHeight = (analysisImg.height * imgWidth) / analysisImg.width;

    if (currentPosition + imgHeight > 280) {
      pdf.addPage();
      currentPosition = 0;
    }

    pdf.addImage(analysisImg, 'JPEG', 20, currentPosition + 20, imgWidth, imgHeight);
    currentPosition += imgHeight + 15;
  }

  return currentPosition;
};

// 加载图片
const loadImage = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = url;
  });
};

// 导出为Word
const exportToWord = async () => {
  proxy.$modal.msgLoading('正在生成Word文档，请稍候...');

  try {
    const children = [];

    // 仅导出题目
    if (!exportForm.includeAnswer && !exportForm.includeAnalysis) {
      for (let i = 0; i < exportQuestions.value.length; i++) {
        const question = exportQuestions.value[i];
        children.push(await createQuestionParagraph(question, i + 1));
      }
    }
    // 分开导出
    else if (exportForm.mergeType === 'separate') {
      // 题目部分
      for (let i = 0; i < exportQuestions.value.length; i++) {
        const question = exportQuestions.value[i];
        children.push(await createQuestionParagraph(question, i + 1));
      }

      // 答案和解析部分
      children.push(new Paragraph({
        text: "答案与解析",
        heading: "Heading1",
        pageBreakBefore: true
      }));

      for (let i = 0; i < exportQuestions.value.length; i++) {
        const question = exportQuestions.value[i];
        children.push(await createAnswerParagraph(question, i + 1));
      }
    }
    // 合并导出
    else {
      if (exportForm.layoutType === 'grouped') {
        // 按题分组：题目+答案+解析
        for (let i = 0; i < exportQuestions.value.length; i++) {
          const question = exportQuestions.value[i];
          children.push(await createQuestionParagraph(question, i + 1));

          if (exportForm.includeAnswer || exportForm.includeAnalysis) {
            children.push(await createAnswerParagraph(question, i + 1));
          }

          // 添加分页符（除了最后一个）
          if (i < exportQuestions.value.length - 1) {
            children.push(new Paragraph({
              text: "",
              pageBreakBefore: true
            }));
          }
        }
      } else {
        // 先题目后答案
        // 题目部分
        for (let i = 0; i < exportQuestions.value.length; i++) {
          const question = exportQuestions.value[i];
          children.push(await createQuestionParagraph(question, i + 1));
        }

        // 答案和解析部分
        children.push(new Paragraph({
          text: "答案与解析",
          heading: "Heading1",
          pageBreakBefore: true
        }));

        for (let i = 0; i < exportQuestions.value.length; i++) {
          const question = exportQuestions.value[i];
          children.push(await createAnswerParagraph(question, i + 1));
        }
      }
    }

    // 创建Word文档
    const doc = new Document({
      sections: [{
        properties: {},
        children: children
      }]
    });

    // 保存Word文档
    const blob = await Packer.toBlob(doc);
    const timestamp = new Date().getTime();
    saveAs(blob, `试题导出_${timestamp}.docx`);

    proxy.$modal.msgSuccess('Word导出成功');
  } catch (error) {
    console.error('导出Word失败:', error);
    proxy.$modal.msgError('导出Word失败，请重试');
  } finally {
    proxy.$modal.msgClose();
  }
};

// 创建题目段落
const createQuestionParagraph = async (question, index) => {
  const img = await loadImage(question.context);
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  // 设置canvas尺寸
  canvas.width = img.width;
  canvas.height = img.height;

  // 绘制图片到canvas
  ctx.drawImage(img, 0, 0);

  // 获取图片数据URL
  const dataUrl = canvas.toDataURL('image/jpeg');

  return new Paragraph({
    children: [
      new ImageRun({
        data: dataUrl.split(',')[1],
        transformation: {
          width: 500,
          height: (img.height * 500) / img.width
        }
      })
    ],
    text: `${index}.`,
    spacing: {
      after: 200
    }
  });
};

// 创建答案段落
const createAnswerParagraph = async (question, index) => {
  const children = [];

  if (exportForm.includeAnswer && question.questionAnswer) {
    const answerImg = await loadImage(question.questionAnswer);
    const answerCanvas = document.createElement('canvas');
    const answerCtx = answerCanvas.getContext('2d');

    answerCanvas.width = answerImg.width;
    answerCanvas.height = answerImg.height;
    answerCtx.drawImage(answerImg, 0, 0);

    const answerDataUrl = answerCanvas.toDataURL('image/jpeg');

    children.push(new ImageRun({
      data: answerDataUrl.split(',')[1],
      transformation: {
        width: 500,
        height: (answerImg.height * 500) / answerImg.width
      }
    }));
  }

  if (exportForm.includeAnalysis && question.questionAnalyze) {
    const analysisImg = await loadImage(question.questionAnalyze);
    const analysisCanvas = document.createElement('canvas');
    const analysisCtx = analysisCanvas.getContext('2d');

    analysisCanvas.width = analysisImg.width;
    analysisCanvas.height = analysisImg.height;
    analysisCtx.drawImage(analysisImg, 0, 0);

    const analysisDataUrl = analysisCanvas.toDataURL('image/jpeg');

    children.push(new ImageRun({
      data: analysisDataUrl.split(',')[1],
      transformation: {
        width: 500,
        height: (analysisImg.height * 500) / analysisImg.width
      }
    }));
  }

  return new Paragraph({
    children: children,
    text: `题目 ${index} 答案与解析:`,
    spacing: {
      after: 200
    }
  });
};
const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    sourcePaper: undefined,
    region: undefined,
    yearList: [],
    knowledgeTreeIds: [],
    difficultyList: [],
    questionTypeList: [],
    regionList: [],
    paperTypeList: [],
    topNode: '', // 存储选中的顶层节点ID
    keyword: '', // 关键词搜索
  },
  queryTreeParams: {
    parentId: '0',
    status: '0',
  }
});
const formatTitle = (title) => {
  if (!title) return '';
  // 查找最后一个冒号的位置
  const lastColonIndex = title.lastIndexOf(':');
  // 如果存在冒号，截取冒号前的部分；否则返回原始标题
  return lastColonIndex > -1 ? title.substring(0, lastColonIndex) : title;
};
const {queryParams, queryTreeParams} = toRefs(data);

// 修改地区格式化方法，兼容数组和字符串格式
const formatRegion = (regionData) => {
  // 处理空值情况
  if (!regionData) {
    return '';
  }

  // 统一将输入转换为数组（确保至少3个元素）
  let regionCodes = [];
  if (Array.isArray(regionData)) {
    // 数组格式：截取前3个元素（确保是省、市、区三级）
    regionCodes = regionData.slice(0, 3);
  } else if (typeof regionData === 'string') {
    // 字符串格式：用逗号分割后取前3个元素
    regionCodes = regionData.split(',').map(code => code.trim()).filter(code => code).slice(0, 3);
  }

  // 确保有3个编码（不足则补空，避免数组长度不够）
  while (regionCodes.length < 3) {
    regionCodes.push('');
  }

  // 解析三级编码（省、市、区）
  const province = codeToText[regionCodes[0]] || ''; // 第一级：省
  const city = codeToText[regionCodes[1]] || '';     // 第二级：市
  const district = codeToText[regionCodes[2]] || ''; // 第三级：区

  // 处理特殊情况：如果市编码无效，但省编码有效（如直辖市）
  const validCity = city || province;
  // 拼接结果（过滤空值）
  const regionNames = [province, validCity, district].filter(name => name);

  // 特殊处理：如果只有一个有效名称（如直辖市）
  if (regionNames.length === 1) {
    return regionNames[0];
  }

  // 正常拼接为“省/市/区”
  return regionNames.join(' / ');
};

/** 通过条件过滤节点 */
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.toLowerCase().includes(value.toLowerCase());
};

/** 过滤知识树 */
const filterKnowledgeTree = () => {
  const knowledgeTreeRef = proxy.$refs.knowledgeTreeRef;
  if (knowledgeTreeRef) {
    knowledgeTreeRef.filter(filterText.value);
  }
};

// 监听过滤文本变化
watch(filterText, (val) => {
  filterKnowledgeTree();
});

// 新增方法 - 导出试题栏中的题目
const exportBoardQuestions = () => {
  if (boardQuestions.value.length === 0) {
    proxy.$message.warning('试题栏中没有试题');
    return;
  }

  // 设置要导出的题目
  exportQuestions.value = [...boardQuestions.value];
  exportDialogVisible.value = true;
};


/** 查询内容下拉树结构 */
function getKnowledgeTree() {
  treeLoading.value = true;

  return selectKnowledgeTreeList(queryTreeParams.value).then(response => {
    try {
      const treeData = proxy.handleTree(response.data, "id");
      knowledgeTreeSelectOptions.value = treeData.map(item => ({
        id: item.id,
        label: item.name
      }));

      if (knowledgeTreeSelectOptions.value.length > 0) {
        queryParams.value.topNode = knowledgeTreeSelectOptions.value[0].id;

        // 获取子树并缓存
        return selectKnowledgeTreeList({ ancestors: queryParams.value.topNode }).then(childResponse => {
          const fullTreeData = proxy.handleTree(childResponse.data, "id");
          fullTreeCache.value[queryParams.value.topNode] = fullTreeData;

          // 直接应用层级筛选
          applyLevelFilter();

          treeLoading.value = false;
          return Promise.resolve();
        });
      }

      // 没有数据时关闭树loading状态
      treeLoading.value = false;
      return Promise.resolve();
    } catch (error) {
      console.error('处理知识树数据失败:', error);
      // 出错时关闭树loading状态
      treeLoading.value = false;
      return Promise.reject(error);
    }
  }).catch(error => {
    console.error('获取知识树失败:', error);
    // 出错时关闭树loading状态
    treeLoading.value = false;
    return Promise.reject(error);
  });
}

/** 处理树选择变化 */
// function handleTreeSelectChange(val) {
//   if (!val) {
//     currentTreeData.value = [];
//     queryParams.value.knowledgeTreeIds = [];
//     return;
//   }
//
//   // 设置查询参数
//   queryParams.value.knowledgeTreeIds = [val];
//
//   // 设置树loading状态（先设置，确保loading立即显示）
//   treeLoading.value = true;
//
//   // 获取选中节点的完整子树结构
//   selectKnowledgeTreeList({ancestors: val}).then(response => {
//     try {
//       // 处理树状结构
//       const fullTreeData = proxy.handleTree(response.data, "id");
//
//       // 找到选中的顶层节点，只展示其子节点
//       const selectedNode = fullTreeData.find(node => node.id === val);
//       if (selectedNode && selectedNode.children) {
//         currentTreeData.value = selectedNode.children;
//       } else {
//         currentTreeData.value = [];
//       }
//
//       // 先关闭树loading状态
//       treeLoading.value = false;
//
//       // 再触发查询，由getList统一处理loading状态
//       getList();
//     } catch (error) {
//       console.error('处理知识树数据失败:', error);
//       // 出错时关闭树loading状态
//       treeLoading.value = false;
//       // 出错时也调用getList以显示无数据状态
//       getList();
//     }
//   }).catch(() => {
//     currentTreeData.value = [];
//     // 出错时关闭树loading状态
//     treeLoading.value = false;
//     // 出错时也调用getList以显示无数据状态
//     getList();
//   });
// }

/** 节点单击事件 */
function handleNodeClick(data) {
  // 只更新查询参数，不修改树结构
  queryParams.value.knowledgeTreeIds = [data.id];

  // 调用getList获取数据，由getList统一处理loading状态
  getList();
}

/** 查询试题列表 */
function getList() {
  // 开启loading效果，依赖v-loading指令
  // 所有调用getList的地方都不需要再单独设置loading状态
  loading.value = true;

  // 清空列表数据，避免显示旧数据
  questionBankList.value = [];

  // 创建新的查询参数对象，排除topNode参数
  const queryParamsForAPI = {...queryParams.value};
  delete queryParamsForAPI.topNode;

  listQuestionBank(proxy.addDateRange(queryParamsForAPI, dateRange.value)).then(async res => {
    try {
      // 初始化展示状态
      const questionList = res.rows.map(item => ({
        ...item,
        selected: false, // 新增选择状态
        showAnalysis: false,  // 控制解析显示
        showAnswer: false,    // 控制答案显示
        // inExport: false,  // 默认不在导出栏中
        // inBoard: false,  // 默认不在试题栏中
      }));

      // 延迟关闭loading，确保DOM渲染完成并让用户看到loading效果
      // 使用统一的延迟时间，确保所有情况下的体验一致
      setTimeout(() => {
        // 更新列表数据
        questionBankList.value = questionList;
        total.value = res.total;
        loading.value = false;
      }, 300);
    } catch (error) {
      console.error('处理试题数据失败:', error);

      // 出现错误时设置空数据
      questionBankList.value = [];
      total.value = 0;

      // 与成功情况使用相同的延迟时间关闭loading
      setTimeout(() => {
        loading.value = false;
        proxy.$message.error('获取试题数据失败');
      }, 300);
    }
  }).catch((error) => {
    console.error('请求试题列表失败:', error);

    // 请求失败时设置空数据
    questionBankList.value = [];
    total.value = 0;

    // 与成功情况使用相同的延迟时间关闭loading
    setTimeout(() => {
      loading.value = false;
      proxy.$message.error('获取试题数据失败');
    }, 300);
  });
}

/** 搜索按钮操作 */
const handleQuery = useDebounceFn(() => {
  // 设置页码为第一页
  queryParams.value.pageNum = 1;

  // 调用getList获取数据，由getList统一处理loading状态
  getList();
}, 300);

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  queryParams.value.sourcePaper = undefined;
  queryParams.value.yearList = [];
  queryParams.value.region = undefined;
  queryParams.value.knowledgeTreeIds = [];
  queryParams.value.difficultyList = [];
  queryParams.value.questionTypeList = [];
  queryParams.value.regionList = [];
  queryParams.value.paperTypeList = [];
  queryParams.value.tag = undefined;
  queryParams.value.keyword = undefined;

  // 如果有数据，重新选择第一个
  if (knowledgeTreeSelectOptions.value.length > 0 && queryParams.value.topNode) {
    // 不要重置已经选择的题库信息，只更新查询参数
    queryParams.value.knowledgeTreeIds = [queryParams.value.topNode];
    // 直接调用查询，跳过树loading
    getList();
  } else {
    // 调用搜索方法获取数据，由getList统一处理loading状态
    handleQuery();
  }
}

/** 删除按钮操作 */
function handleDelete(id) {
  proxy.$modal.confirm('是否确认删除该试题？').then(function () {
    return delQuestionBank(id);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "试题导入";
  upload.open = true;
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("system/user/importTemplate", {}, `user_template_${new Date().getTime()}.xlsx`);
}

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].handleRemove(file);
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", {dangerouslyUseHTMLString: true});
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

// 打开试题栏抽屉
const openQuestionDrawer = async () => {
  drawerVisible.value = true;
  currentDrawerType.value = 'board';
  await getQuestionBoardList('board');
};

// 打开导出栏抽屉
const openExportDrawer = async () => {
  exportDrawerVisible.value = true;
  currentDrawerType.value = 'export';
  await getQuestionBoardList('export');
};

// 获取试题栏或导出栏数据
const getQuestionBoardList = async (type) => {
  try {
    const res = await listQuestionBankBoard(type);
    if (res.code === 200) {
      if (type === 'board') {
        boardQuestions.value = res.data || [];
        boardCount.value = boardQuestions.value.length;
      } else if (type === 'export') {
        exportQuestions.value = res.data || [];
        exportCount.value = exportQuestions.value.length;
      }
    } else {
      proxy.$message.error(res.msg || `获取${type === 'board' ? '试题栏' : '导出栏'}数据失败`);
    }
  } catch (error) {
    console.error(`获取${type === 'board' ? '试题栏' : '导出栏'}数据失败:`, error);
    proxy.$message.error(`获取${type === 'board' ? '试题栏' : '导出栏'}数据失败`);
  }
};

// 添加试题到试题栏或导出栏
const addToQuestionBoard = async (type, question, event) => {
  try {
    // 获取最近的按钮组容器
    const buttonGroup = event?.target?.closest('.button-group');

    // 使用统一的loading效果
    const buttonLoading = proxy.$loading({
      target: buttonGroup || document.body,
      text: '添加中...',
      background: 'rgba(255, 255, 255, 0.9)'
    });

    const res = await addQuestionBankBoard(type, question.id);
    if (res.code === 200) {
      proxy.$message.success('已添加成功');

      // 更新对应栏的状态
      if (type === 'board') {
        question.inBoard = true;
        await getQuestionBoardList('board');
      } else if (type === 'export') {
        question.inExport = true;
        await getQuestionBoardList('export');
      }
    } else {
      proxy.$message.error(res.msg || '添加失败');
    }
    buttonLoading.close();
  } catch (error) {
    console.error('添加失败:', error);
    proxy.$message.error('添加失败');
  }
};

// 从试题栏或导出栏移除试题
const removeFromQuestionBoard = async (type, id) => {
  try {
    // 区分是从主列表中移除还是从抽屉中移除
    const isFromDrawer = (type === 'board' && drawerVisible.value) ||
        (type === 'export' && exportDrawerVisible.value);
    let loadingInstance;

    if (isFromDrawer) {
      // 如果是从抽屉中移除，使用局部loading
      loadingInstance = proxy.$loading({
        target: document.querySelector(type === 'board' ? '.question-drawer' : '.export-drawer'),
        text: '正在移除...',
        background: 'rgba(255, 255, 255, 0.9)'
      });
    } else {
      // 如果是从主列表中移除，只在按钮区域显示loading
      loadingInstance = proxy.$loading({
        target: event?.target?.closest('.button-group') || document.body,
        text: '正在移除...',
        background: 'rgba(255, 255, 255, 0.9)'
      });
    }

    const res = await delQuestionBankBoard(type, id);
    if (res.code === 200) {
      proxy.$message.success('移除成功');

      // 更新主列表中对应试题的状态
      const question = questionBankList.value.find(item => item.id === id);
      if (question) {
        if (type === 'board') {
          question.inBoard = false;
        } else if (type === 'export') {
          question.inExport = false;
        }
      }

      // 更新对应栏的数据
      await getQuestionBoardList(type);
    } else {
      proxy.$message.error(res.msg || '移除失败');
    }

    loadingInstance.close();
  } catch (error) {
    console.error('移除失败:', error);
    proxy.$message.error('移除失败');
  }
};

// 清空试题栏或导出栏
const handleClearBoard = async (type) => {
  try {
    await proxy.$confirm('确定要清空吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const res = await delQuestionBankBoard(type, 'all');
    if (res.code === 200) {
      proxy.$message.success('已清空');

      // 更新主列表中所有试题的状态
      questionBankList.value.forEach(item => {
        if (type === 'board') {
          item.inBoard = false;
        } else if (type === 'export') {
          item.inExport = false;
        }
      });

      // 更新对应栏的数据
      if (type === 'board') {
        boardQuestions.value = [];
        boardCount.value = 0;
      } else if (type === 'export') {
        exportQuestions.value = [];
        exportCount.value = 0;
      }
    } else {
      proxy.$message.error(res.msg || '清空失败');
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空失败:', error);
      proxy.$message.error('清空失败');
    }
  }
};

// 手动组卷
const handleManualPaper = () => {
  router.push('/qh/testhand/index');
};

// 修改切换显示函数，支持两种类型
const toggleAnswer = (row, type) => {
  if (type === 'analysis') {
    row.showAnalysis = !row.showAnalysis;
  } else if (type === 'answer') {
    row.showAnswer = !row.showAnswer;
  }
};

// 添加上传答案功能
const handleUploadAnswer = (row) => {
  currentQuestion.value = row;
  uploadAnswerVisible.value = true;  // 需要定义这个ref
  previewImage.value = '';
  uploadFile.value = null;
  imageError.value = '';
};

// 上传答案图片处理
const submitUploadAnswer = async () => {
  if (!uploadFile.value || !currentQuestion.value) {
    proxy.$message.warning('请先选择答案图片');
    return;
  }

  try {
    uploadLoading.value = true;
    const res = await fileUpload(uploadFile.value.raw);
    if (res.code === 200) {
      const imageUrl = res.msg;
      // 更新试题答案
      await updateQuestionBank({
        id: currentQuestion.value.id,
        questionAnswer: imageUrl
      });

      // 关闭对话框并刷新列表
      uploadAnswerVisible.value = false;
      previewImage.value = '';
      uploadFile.value = null;
      uploadLoading.value = false;
      proxy.$message.success('答案上传成功');
      getList();
    } else {
      uploadLoading.value = false;
      proxy.$message.error(res.msg || '答案图片上传失败');
    }
  } catch (error) {
    console.error('上传答案失败:', error);
    uploadLoading.value = false;
    proxy.$message.error('上传答案失败');
  }
};

// 处理上传解析
const handleUploadAnalysis = (row) => {
  currentQuestion.value = row;
  uploadAnalysisVisible.value = true;
  previewImage.value = '';
  uploadFile.value = null;
  imageError.value = '';
};

// 文件选择处理
const handleFileChange = (file) => {
  if (!file) return;
  uploadFile.value = file;

  // 创建图片对象检查尺寸
  const img = new Image();
  img.onload = () => {
    imageError.value = '';
    // 生成预览
    previewImage.value = URL.createObjectURL(file.raw);
  };

  img.onerror = () => {
    imageError.value = '图片加载失败，请重新选择';
    uploadRef.value.clearFiles();
    uploadFile.value = null;
  };

  // 加载图片以触发验证
  img.src = URL.createObjectURL(file.raw);
};

// 取消上传
const cancelUpload = () => {
  previewImage.value = '';
  imageError.value = '';
  uploadFile.value = null;
  uploadRef.value.clearFiles();
};

// 提交上传解析
const submitUploadAnalysis = async () => {
  if (!uploadFile.value || !currentQuestion.value) {
    proxy.$message.warning('请先选择图片');
    return;
  }

  try {
    uploadLoading.value = true;

    // 直接传递文件对象给上传接口
    const res = await fileUpload(uploadFile.value.raw);
    if (res.code === 200) {
      const imageUrl = res.msg;
      // 更新试题解析
      await updateQuestionBank({
        id: currentQuestion.value.id,
        questionAnalyze: imageUrl
      });

      // 关闭对话框并刷新列表
      uploadAnalysisVisible.value = false;
      previewImage.value = '';
      uploadFile.value = null;
      uploadLoading.value = false;
      proxy.$message.success('解析上传成功');
      getList();
    } else {
      uploadLoading.value = false;
      proxy.$message.error(res.msg || '图片上传失败');
    }
  } catch (error) {
    console.error('上传解析失败:', error);
    uploadLoading.value = false;
    proxy.$message.error('上传解析失败');
  }
};

// 添加拖拽开始处理方法
const onDragStart = () => {
  // 拖拽开始时保存原始顺序
  if (currentDrawerType.value === 'board') {
    originalBoardQuestions.value = [...boardQuestions.value];
  } else if (currentDrawerType.value === 'export') {
    originalBoardQuestions.value = [...exportQuestions.value];
  }
};

// 添加拖拽结束处理方法
const onDragEnd = async () => {
  try {
    // 根据当前抽屉类型确定要更新的数据
    const data = currentDrawerType.value === 'board'
        ? boardQuestions.value
        : exportQuestions.value;

    // 比较拖拽前后顺序是否有变化
    const hasOrderChanged = originalBoardQuestions.value.some((item, index) => {
      return item.id !== data[index]?.id;
    });

    // 如果顺序没有变化，则不调用后端接口
    if (!hasOrderChanged) {
      return;
    }

    // 组装更新后的顺序数据
    const orderData = data.map((item, index) => ({
      id: item.id,
      orderNum: index + 1
    }));

    const res = await updateQuestionBankBoardOrder(currentDrawerType.value, orderData);
    if (res.code === 200) {
      proxy.$message.success('试题顺序已更新');
    } else {
      proxy.$message.error(res.msg || '试题顺序更新失败');
      // 更新失败时重新获取数据
      await getQuestionBoardList(currentDrawerType.value);
    }
  } catch (error) {
    console.error('更新试题顺序失败:', error);
    proxy.$message.error('更新试题顺序失败');
    await getQuestionBoardList(currentDrawerType.value);
  }
};

// 跳转到试题录入页面
const handleAddQuestion = () => {
  router.push('/qh/questionEntry-index/index');
};

onMounted(() => {
  // 确保知识树loading在组件挂载时已经显示
  treeLoading.value = true;

  // 先获取知识树，然后在回调中获取试题列表
  getKnowledgeTree().then(() => {
    // 确保知识树数据加载完成后再获取试题列表
    // getList函数会设置loading状态
    getList();
  }).catch(error => {
    console.error('获取知识树数据失败:', error);
    // 发生错误时也调用getList以显示无数据状态
    getList();
  });
});
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;

  margin-bottom: 0;
  padding-right: 80px;
  overflow-y: auto;
}

/* 知识树样式 */
.head-container {
  border: 1px solid #ebeef5;
  border-radius: 6px;
  padding: 5px;
  background: #fff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  position: relative; /* 确保相对定位以支持loading效果 */
  min-height: 200px; /* 添加最小高度确保loading显示 */
}

.head-container:hover {
  border-color: #409eff;
  box-shadow: 0 2px 2px 0 rgba(64, 158, 255, 0.1);
}

.tree-search-container {
  padding: 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.library-select {
  width: 100%;
  margin-bottom: 10px;
  font-size: 14px;
}

.filter-input {
  width: 100%;
  margin-bottom: 10px;
  font-size: 14px;
}

.tree-container {
  height: calc(100vh - 270px);
  overflow-y: auto;
  /*border: 1px solid #ebeef5;*/
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 22px;
}

.tree-node-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.node-label {
  margin-left: 5px;
  font-size: 14px;
  line-height: 1.5;
}

.knowledge-tree {
  font-size: 14px;
  font-weight: 500;
  padding-right: 1px;
}

.knowledge-tree :deep(.el-tree-node__content) {
  height: 36px;
  padding: 6px 0;
  margin: 2px 0;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.knowledge-tree :deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  font-weight: bold;
  color: #409eff;
  border-left: 3px solid #409eff;
}

.knowledge-tree :deep(.el-tree-node__content:hover) {
  background-color: #f5f7fa;
  transform: translateX(3px);
}

.knowledge-tree :deep(.el-tree-node__children) {
  padding-left: 16px;
}

/* 滚动效果 */
.head-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
  transition: all 0.3s 0.2s;
}

.head-container::-webkit-scrollbar-thumb {
  background-color: rgba(192, 196, 204, 0);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.head-container:hover::-webkit-scrollbar {
  background-color: #f5f7fa;
}

.head-container:hover::-webkit-scrollbar-thumb {
  background-color: #c0c4cc;
}

/* 试题列表样式 */
.scroll-container {
  height: calc(100vh - 270px);
  overflow-y: auto;
  border-radius: 8px;
  padding-right: 0;
  padding-left: 0;
  margin: 0;
  position: relative; /* 添加相对定位以支持loading定位 */
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 5px;
  background: #f8fbff;
  border-radius: 6px 6px 0 0;
  justify-content: space-between;
  margin-bottom: 0;
  padding: 8px 15px;
  border-bottom: 1px solid #ebeef5;
}

.type-tag,
.difficulty-tag {
  font-size: 18px;
  font-weight: 520;
  padding: 6px 5px;
  transform: scale(1.15);
}

.knowledge-box {
  background: #e6f7ff;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 !important;
}

.knowledge-box .meta-info-label {
  color: #666;
}

.knowledge-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.knowledge-item {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  color: #409eff;
  padding: 2px 1px;
  border-radius: 3px;
}

.separator {
  color: #c0c4cc;
  margin-left: 8px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-left: auto;
  font-size: 12px;
}

.question-button {
  width: 90px;
  height: 27px;
  margin: auto;
  border-radius: 4px;
  padding: 8px 15px;
  transition: all 0.3s;
}

.upload-analysis-btn {
  background-color: #e8f5e9 !important;
  border-color: #c8e6c9 !important;
  color: #2e7d32 !important;
  border-radius: 4px;
  transition: all 0.3s;
  width: 90px;
  height: 27px;
  margin: auto;
}

.upload-analysis-btn:hover {
  background-color: #d4edda !important;
  border-color: #c3e6cb !important;
  color: #155724 !important;
}

.upload-analysis-btn:active {
  background-color: #c3e6cb !important;
}

.context-image {
  border-radius: 4px;
  border: none;
  display: flex;
  justify-content: left;
  float: none;
  align-items: flex-start;
  position: relative;
  max-width: 800px;
  min-width: 600px;
  margin: 15px auto;
  padding: 0 30px;
}

.index-image-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 1px;
}

.question-index {
  position: absolute;
  top: 17px;
  left: 20px;
  color: #333;
  font-size: 16px;
  z-index: 10;
}

.tags-container {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding-right: 5px;
}

@media (max-width: 768px) {
  .tags-container {
    gap: 8px;
  }
}

.image-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.empty-description {
  font-size: 16px;
  color: #606266;
  margin-bottom: 20px;
  font-weight: 500;
}

/* 浮动按钮 */
.floating-button {
  position: fixed;
  right: 10px;
  top: 45%;
  transform: translateY(-50%);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

/* 浮动按钮 */
.export-floating-button {
  position: fixed;
  right: 10px;
  top: 55%;
  transform: translateY(-50%);
  z-index: 2000;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.floating-button:hover .export-floating-button {
  transform: translateY(-50%) scale(1.05);
}

.floating-button:hover .button-text .export-floating-button {
  opacity: 1;
  transform: translateX(0);
}

.button-icon {
  width: 60px;
  height: 60px;
  font-size: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  position: relative;
}

.button-icon:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.button-text {
  margin-top: 8px;
  color: #606266;
  font-size: 14px;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
  white-space: nowrap;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f56c6c;
  color: white;
  border-radius: 50%;
  min-width: 22px;
  height: 22px;
  line-height: 22px;
  font-size: 12px;
  text-align: center;
  padding: 0 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 抽屉样式 */
.question-drawer {
  z-index: 2001 !important;
}

.question-drawer :deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
}

.drawer-title {
  font-size: 18px;
  font-weight: 500;
}

.drawer-actions {
  display: flex;
  gap: 10px;
}

.selected-question {
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.selected-question .question-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.selected-question .question-index {
  position: relative;
  top: 0;
  left: 0;
  font-weight: 500;
  margin-right: 10px;
  font-size: 16px;
}

.selected-question .question-meta {
  flex: 1;
  display: flex;
  gap: 8px;
  align-items: center;
}

.selected-question .knowledge {
  color: #409eff;
  font-size: 14px;
}

.selected-question .question-image {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
}

.empty-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* 上传解析样式 */
.upload-analysis-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.analysis-uploader {
  width: 100%;
}

.mb-3 {
  margin-bottom: 12px;
}

.upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  width: 450px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.upload-area:hover {
  border-color: #409eff;
}

.upload-icon {
  font-size: 28px;
  color: #8c939d;
  margin-bottom: 10px;
}

.upload-text {
  color: #606266;
  font-size: 14px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #909399;
  font-size: 12px;
}

.preview-image {
  width: 100%;
  max-height: 200px;
  object-fit: contain;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.preview-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
  gap: 15px;
}

.question-item {
  margin-bottom: 10px;
  border: 2px solid #ebeef5 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

/* 添加最后一个元素的样式 */
.question-list .question-item:last-child {
  margin-bottom: 0;
}

.question-item:hover {
  border-color: #d1e9ff !important;
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 5px;
  background: #f8fbff;
  border-radius: 6px 6px 0 0;
  justify-content: space-between;
  margin-bottom: 0;
  padding: 8px 15px;
  border-bottom: 1px solid #ebeef5;
}

.context-image {
  border-radius: 4px;
  border: none;
  display: flex;
  justify-content: left;
  float: none;
  align-items: flex-start;
  position: relative;
  max-width: 800px;
  min-width: 600px;
  margin: 15px auto;
  padding: 0 30px;
}

.question-source {
  font-size: 14px;
  color: #606266;
  margin-right: auto;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.source-info-item {
  background-color: #f5f6f6;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.source-tag-item {
  color: #59bd07;
  background-color: #f5f6f6;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.answer-control {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #fafafa;
  border-top: 1px dashed #ebeef5;
}

.custom-zoom-enter-active,
.custom-zoom-leave-active {
  transition: all 0.3s ease;
}

.custom-zoom-enter-from,
.custom-zoom-leave-to {
  opacity: 0;
  transform: scale(0.95);
}

/* 拖拽相关样式 */
.draggable-container {
  min-height: 10px;
}

.drag-icon {
  margin-right: 8px;
  padding: 4px;
  color: #909399;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
}

.selected-question {
  transition: all 0.3s ease;
  position: relative;
  background-color: #fff;
  border-left: 3px solid transparent;
  cursor: move; /* 显示拖动光标 */
  user-select: none; /* 防止文本选择 */
}

.selected-question:hover {
  background-color: #f8f9fa;
  border-left: 3px solid #409eff;
}

.selected-question:hover .drag-icon {
  color: #409eff;
  background-color: #f0f2f5;
}

.selected-question.sortable-chosen {
  background-color: #f0f7ff;
  box-shadow: 0 5px 15px 0 rgba(0, 0, 0, 0.15);
  z-index: 10;
  transform: scale(1.01);
  border-left: 3px solid #409eff;
}

.selected-question.sortable-ghost {
  opacity: 0.6;
  background-color: #e6f7ff;
  border: 2px dashed #409eff;
  border-radius: 8px;
}

/* 调整question-header布局 */
.question-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 5px;
}

/* loading样式优化 */
.question-list {
  min-height: 400px;
}

.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9) !important;
  z-index: 1000 !important;
}

.el-loading-spinner {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  margin: 0 !important;
}

.el-loading-spinner .circular {
  width: 60px !important;
  height: 60px !important;
}

.el-loading-spinner .el-loading-text {
  color: #409EFF !important;
  font-size: 16px !important;
  margin-top: 15px !important;
  font-weight: bold !important;
}

/* 添加导出对话框样式 */
.export-dialog {
  z-index: 2500 !important;
}

.export-form-item {
  margin-bottom: 20px;
}

.export-option-label {
  font-weight: bold;
  margin-bottom: 8px;
  display: block;
}

/* 新增选择框样式 */
.selection-control {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 10;
}

.question-item {
  position: relative; /* 为选择框提供定位上下文 */
}

/* 当选中时添加背景色 */
.question-item.selected {
  background-color: #f0f7ff;
  border-left: 3px solid #409eff;
}

/* 调整浮动按钮位置 */
.floating-button {
  top: 50%; /* 居中位置 */
  transform: translateY(-50%);
}

.export-floating-button {
  top: 60%; /* 在试题栏下方 */
}

.level-select {
  width: 100%;
  margin-bottom: 10px;
}

/* 调整搜索容器布局 */
.tree-search-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* 调整抽屉按钮布局 */
.drawer-actions {
  display: flex;
  gap: 8px;
}

/* 导出对话框样式 */
.export-dialog {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.15);
}

.export-dialog :deep(.el-dialog__header) {
  background: linear-gradient(120deg, #409eff, #79bbff);
  margin: 0;
  padding: 16px 20px;
}

.export-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.dialog-content {
  padding: 20px 25px;
}

.form-section {
  padding: 15px 0;
  border-bottom: 1px dashed #eaeef5;
  margin-bottom: 15px;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 5px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
}

.section-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
}

.export-form-item {
  margin-bottom: 22px;
}

.radio-group {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.radio-group :deep(.el-radio) {
  margin: 0;
  height: auto;
  padding: 12px 15px;
  border-radius: 8px;
  transition: all 0.3s;
}

.radio-group :deep(.el-radio.is-bordered) {
  border: 1px solid #dcdfe6;
}

.radio-group :deep(.el-radio.is-bordered.is-checked) {
  border-color: #409eff;
  background-color: #ecf5ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.radio-group :deep(.el-radio__label) {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.radio-group .el-icon {
  margin-right: 6px;
  font-size: 16px;
}

.dialog-footer {
  padding: 0 25px 20px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  padding: 9px 20px;
  border-radius: 6px;
  font-weight: 500;
}

.dialog-footer .el-button:last-child {
  background: linear-gradient(120deg, #409eff, #79bbff);
  border: none;
  color: white;
}

.dialog-footer .el-button:last-child:hover {
  opacity: 0.9;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.dialog-footer .el-icon {
  margin-right: 6px;
}
</style>
