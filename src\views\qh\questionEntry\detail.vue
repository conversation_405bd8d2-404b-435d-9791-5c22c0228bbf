<template>
  <div class="app-container">
    <!-- 顶部文件信息 -->
    <div class="file-header" v-if="uploadedFile">
      <div class="file-info">
        <el-icon class="file-icon">
          <document/>
        </el-icon>
        <span class="file-name">{{ uploadedFile.name }}</span>
      </div>
    </div>

    <div v-loading="loading" element-loading-text="试卷加载中，请耐心等待..." class="content-container">
      <!-- 试卷信息区域 -->
      <el-card shadow="hover" class="info-card">
        <div class="card-header">
          <span>试卷信息</span>
          <el-tag type="info" size="small">共{{ questions.length }}题</el-tag>
        </div>
        <el-form
            :model="examInfo"
            :rules="examInfoRules"
            ref="examInfoRef"
            label-width="100px"
            class="info-form"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="试卷名称" prop="paperName">
                <el-input
                    :value="displayPaperName"
                    @input="updatePaperName"
                    class="info-input"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="试卷类型" prop="paperType">
                <el-select v-model="examInfo.paperType" clearable placeholder="卷型选择"
                           class="info-select">
                  <el-option v-for="dict in sys_qh_paper_type" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="试卷年份" prop="pyear">
                <el-date-picker
                    v-model="examInfo.pyear"
                    type="year"
                    value-format="YYYY"
                    placeholder="选择年份"
                    class="info-date-picker"
                    style="width: 100%;"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="8">
              <el-form-item label="所属地区" prop="region">
                <!-- 修复地区选择器值绑定问题 -->
                <el-cascader
                    v-model="examInfo.region"
                    :options="regionData"
                    :props="{ value: 'value', label: 'label', children: 'children' }"
                    clearable
                    placeholder="请选择省市区"
                    class="info-select"
                    style="width: 100%;"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属年级" prop="grade">
                <el-select v-model="examInfo.grade" clearable placeholder="年级选择"
                           class="info-select">
                  <el-option v-for="dict in sys_qh_grade" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所属学科" prop="subject">
                <el-select v-model="examInfo.subject" clearable placeholder="学科选择"
                           class="info-select">
                  <el-option v-for="dict in sys_qh_subject" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 题目编辑区域 -->
      <el-card v-if="currentQuestion" shadow="hover" class="question-card">
        <div class="card-header">
          <div class="question-header-left">
            <span>题目编辑</span>
            <el-tag :type="currentQuestion.submitted ? 'success' : 'warning'" size="default">
              {{ currentQuestion.submitted ? '已录题' : '待录题' }}
            </el-tag>
            <el-button
                :type="rememberAttributes ? 'success' : 'default'"
                size="default"
                @click="toggleRememberAttributes"
                class="remember-btn"
            >
              <el-icon>
                <check v-if="rememberAttributes"/>
                <clock v-else/>
              </el-icon>
              {{ rememberAttributes ? '已记忆属性' : '记忆试题属性' }}
            </el-button>
          </div>
          <div class="question-counter">
            第 {{ currentIndex + 1 }} 题 / 共 {{ questions.length }} 题
          </div>
        </div>

        <el-form
            :model="currentQuestion"
            :rules="questionRules"
            ref="questionRef"
            label-width="100px"
            class="question-form"
        >
          <!-- 题目图片、答案图片和解析图片 -->
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="8">
              <el-form-item label="题目图片" prop="context">
                <div class="image-container">
                  <div
                      class="image-preview"
                      v-if="currentQuestion.context"
                      @click="openImagePreview(currentQuestion.context)"
                  >
                    <img
                        :src="getImageUrl(currentQuestion.context)"
                        :alt="`题目图片 ${currentIndex + 1}`"
                        @error="handleImageError('question')"
                        class="preview-image"
                    >
                    <div class="zoom-indicator">
                      <el-icon>
                        <zoom-in/>
                      </el-icon>
                    </div>
                  </div>
                  <div class="placeholder" v-else>
                    <el-icon>
                      <picture/>
                    </el-icon>
                    <span>暂无题目图片</span>
                  </div>

                  <el-upload
                      class="image-uploader"
                      action="#"
                      :auto-upload="false"
                      :on-change="(file) => handleImageUpload(file, 'question')"
                      :show-file-list="false"
                      accept="image/jpeg,image/png,image/jpg"
                      :disabled="currentQuestion.submitted || uploadLoading"
                  >
                    <el-button
                        type="primary"
                        size="small"
                        class="upload-btn"
                        :disabled="currentQuestion.submitted || uploadLoading"
                    >
                      <el-icon v-if="!uploadLoading">
                        <upload/>
                      </el-icon>
                      <el-icon v-if="uploadLoading" class="upload-loading">
                        <loading/>
                      </el-icon>
                      {{ uploadLoading ? '上传中...' : '重新上传' }}
                    </el-button>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
            <!-- 新增答案图片 -->
            <el-col :span="8">
              <el-form-item label="答案图片" prop="questionAnswer">
                <div class="image-container">
                  <div
                      class="image-preview"
                      v-if="currentQuestion.questionAnswer"
                      @click="openImagePreview(currentQuestion.questionAnswer)"
                  >
                    <img
                        :src="getImageUrl(currentQuestion.questionAnswer)"
                        :alt="`答案图片 ${currentIndex + 1}`"
                        @error="handleImageError('answer')"
                        class="preview-image"
                    >
                    <div class="zoom-indicator">
                      <el-icon>
                        <zoom-in/>
                      </el-icon>
                    </div>
                  </div>
                  <div class="placeholder" v-else>
                    <el-icon>
                      <picture/>
                    </el-icon>
                    <span>暂无答案图片</span>
                  </div>

                  <el-upload
                      class="image-uploader"
                      action="#"
                      :auto-upload="false"
                      :on-change="(file) => handleImageUpload(file, 'answer')"
                      :show-file-list="false"
                      accept="image/jpeg,image/png,image/jpg"
                      :disabled="currentQuestion.submitted || uploadLoading"
                  >
                    <el-button
                        type="primary"
                        size="small"
                        class="upload-btn"
                        :disabled="currentQuestion.submitted || uploadLoading"
                    >
                      <el-icon v-if="!uploadLoading">
                        <upload/>
                      </el-icon>
                      <el-icon v-if="uploadLoading" class="upload-loading">
                        <loading/>
                      </el-icon>
                      {{ uploadLoading ? '上传中...' : '重新上传' }}
                    </el-button>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="解析图片" prop="questionAnalyze">
                <div class="image-container">
                  <div
                      class="image-preview"
                      v-if="currentQuestion.questionAnalyze"
                      @click="openImagePreview(currentQuestion.questionAnalyze)"
                  >
                    <img
                        :src="getImageUrl(currentQuestion.questionAnalyze)"
                        :alt="`解析图片 ${currentIndex + 1}`"
                        @error="handleImageError('answer')"
                        class="preview-image"
                    >
                    <div class="zoom-indicator">
                      <el-icon>
                        <zoom-in/>
                      </el-icon>
                    </div>
                  </div>
                  <div class="placeholder" v-else>
                    <el-icon>
                      <picture/>
                    </el-icon>
                    <span>暂无解析图片</span>
                  </div>

                  <el-upload
                      class="image-uploader"
                      action="#"
                      :auto-upload="false"
                      :on-change="(file) => handleImageUpload(file, 'analysis')"
                      :show-file-list="false"
                      accept="image/jpeg,image/png,image/jpg"
                      :disabled="currentQuestion.submitted || uploadLoading"
                  >
                    <el-button
                        type="primary"
                        size="small"
                        class="upload-btn"
                        :disabled="currentQuestion.submitted || uploadLoading"
                    >
                      <el-icon v-if="!uploadLoading">
                        <upload/>
                      </el-icon>
                      <el-icon v-if="uploadLoading" class="upload-loading">
                        <loading/>
                      </el-icon>
                      {{ uploadLoading ? '上传中...' : '重新上传' }}
                    </el-button>
                  </el-upload>
                </div>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 其他题目信息行 -->
          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="12">
              <el-form-item label="试题类型" prop="questionType">
                <el-select
                    v-model="currentQuestion.questionType"
                    placeholder="题型选择"
                    class="question-select"
                    :disabled="currentQuestion.submitted"
                >
                  <el-option v-for="dict in sys_qh_questions_type" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试题标签" prop="tag">
                <el-input
                    v-model="currentQuestion.tag"
                    clearable
                    placeholder="试题标签"
                    class="question-input"
                    :disabled="currentQuestion.submitted"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="12">
              <el-form-item label="所属题库" prop="bankId">
                <el-select
                    v-model="currentQuestion.bankId"
                    placeholder="请选择所属题库"
                    class="question-select"
                    :disabled="currentQuestion.submitted"
                    @change="handleBankChange"
                >
                  <el-option
                      v-for="item in knowledgeTreeRoots"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- 修改：试题考点支持多选 -->
              <el-form-item label="试题考点" prop="knowledgeTreeIds">
                <el-cascader
                    v-model="currentQuestion.knowledgeTreeIds"
                    :options="currentKnowledgeTreeOptions"
                    :props="{
                      label: 'name',
                      value: 'id',
                      children: 'children',
                      multiple: true, // 启用多选模式
                      emitPath: false  // 只返回选中的节点值，不返回路径
                    }"
                    :show-all-levels="true"
                    :disabled="currentQuestion.submitted"
                    placeholder="请选择试题考点"
                    clearable
                    filterable
                    class="question-select"
                    style="width: 100%;"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="form-row-margin">
            <el-col :span="12">
              <el-form-item label="试题难度" prop="difficulty">
                <el-select
                    v-model="currentQuestion.difficulty"
                    placeholder="难度选择"
                    class="question-select"
                    :disabled="currentQuestion.submitted"
                >
                  <el-option v-for="dict in sys_qh_difficulty" :key="dict.value" :label="dict.label"
                             :value="dict.value"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="试题内容" prop="ocrText">
                <el-input
                    type="textarea"
                    v-model="currentQuestion.ocrText"
                    clearable
                    placeholder="该内容用于试题列表中搜索使用"
                    :autosize="{ minRows: 4 }"
                    class="question-textarea"
                    :disabled="currentQuestion.submitted"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="action-buttons">
          <!-- 左侧：新增和删除按钮组 -->
          <div class="edit-buttons">
            <el-button
                type="primary"
                @click="addNewQuestion"
                class="add-btn"
                :disabled="loading"
            >
              <el-icon>
                <plus/>
              </el-icon>
              新增题目
            </el-button>
            <el-button
                type="danger"
                @click="deleteQuestion"
                class="delete-btn"
                :disabled="questions.length <= 1"
            >
              <el-icon>
                <delete/>
              </el-icon>
              删除题目
            </el-button>
          </div>

          <!-- 中间：导航按钮组 -->
          <el-button-group class="navigation-buttons">
            <el-button
                :disabled="currentIndex === 0"
                @click="prevQuestion"
                class="nav-btn"
            >
              <el-icon>
                <arrow-left/>
              </el-icon>
              上一题
            </el-button>
            <el-button
                :disabled="currentIndex >= questions.length - 1"
                @click="nextQuestion"
                class="nav-btn"
            >
              下一题
              <el-icon>
                <arrow-right/>
              </el-icon>
            </el-button>
          </el-button-group>

          <!-- 右侧：录题和全部提交按钮组（紧凑排列） -->
          <div class="submit-buttons">
            <el-button
                type="primary"
                @click="submitQuestion"
                :disabled="!canSubmitQuestion"
                class="submit-btn"
            >
              <el-icon>
                <check/>
              </el-icon>
              {{ currentQuestion.submitted ? '已录题' : '录题' }}
            </el-button>
            <el-button
                type="success"
                @click="submitAll"
                :disabled="!allSubmitted || submitLoading"
                class="submit-all-btn"
            >
              <el-icon v-if="!submitLoading">
                <finished/>
              </el-icon>
              <el-icon v-if="submitLoading" class="loading-icon">
                <loading/>
              </el-icon>
              {{ submitLoading ? '提交中...' : '全部提交' }}
            </el-button>
          </div>
        </div>
      </el-card>

      <div v-else-if="!loading" class="no-question提示">
        <el-empty description="暂无题目，请新增题目"></el-empty>
      </div>
    </div>

    <div class="stats-bar" v-if="questions.length > 0">
      <el-progress
          :percentage="submittedPercentage"
          :color="customColors"
          :stroke-width="16"
          :show-text="false"
          class="progress-bar"
      />
      <div class="stats-info">
        <span class="submitted">已录题: <strong>{{ submittedCount }}</strong></span>
        <span class="total">总题目: <strong>{{ questions.length }}</strong></span>
        <span class="percentage">完成度: <strong>{{ submittedPercentage }}%</strong></span>
      </div>
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog
        v-model="imagePreviewVisible"
        title="图片预览"
        width="90%"
        height="90%"
        :close-on-click-modal="true"
    >
      <div class="preview-container">
        <img
            :src="previewImageUrl"
            alt="图片预览"
            class="preview-img"
            @load="adjustPreviewImage"
        >
      </div>
    </el-dialog>

    <!-- 添加悬浮按钮 -->
    <el-tooltip
        effect="dark"
        content="添加知识点"
        placement="left"
        :disabled="!showFloatingButton"
    >
      <div
          class="floating-button"
          :class="{ disabled: !currentQuestion?.bankId }"
          @click="handleAddKnowledgeTree"
      >
        <el-icon class="add-icon"><Plus /></el-icon>
      </div>
    </el-tooltip>
  </div>
</template>

<script setup>
import {computed, getCurrentInstance, onMounted,  onUnmounted,reactive, ref, watch} from 'vue'
import {
  ArrowLeft,
  ArrowRight,
  Check,
  Clock,
  Delete,
  Document,
  Finished,
  Loading,
  Picture,
  Plus,
  Upload,
  ZoomIn
} from '@element-plus/icons-vue'
import {ElEmpty, ElMessage, ElMessageBox} from 'element-plus'
import {useRoute, useRouter} from 'vue-router'
import {uploadResult} from "@/api/qh/paperUpload.js";
import {addQuestionBank} from "@/api/qh/questionBank.js";
import {selectKnowledgeTreeList} from "@/api/qh/knowledgeTree.js";
import {fileUpload} from "@/api/qh/minio.js";
import {codeToText, regionData} from "element-china-area-data";

const router = useRouter();
const route = useRoute()

// 存储上传的文件信息
const uploadedFile = ref(null)
const loading = ref(true)
const uploadLoading = ref(false)
// 新增：全部提交loading状态
const submitLoading = ref(false)

const examInfo = reactive({
  paperName: '',
  paperType: '',
  pyear: '',
  region: ['110000', '110100', '110101'],
  grade: '',
  subject: ''
})
const questions = ref([])
const currentIndex = ref(0)
const rememberAttributes = ref(true)  // 默认开启
const savedAttributes = ref({
  questionType: '',
  difficulty: '',
  bankId: '',
  knowledgeTreeIds: []
})
const questionAttributes = ref({})
const examInfoRef = ref(null)
const questionRef = ref(null)
const examInfoValid = ref(false)
const questionValid = ref(false)

// 知识树相关数据
const knowledgeTreeList = ref([])
const knowledgeTreeRoots = ref([])
const knowledgeTreeMap = ref({})
const knowledgeTreeChildrenMap = ref({})

// 图片预览相关
const imagePreviewVisible = ref(false)
const previewImageUrl = ref('')

const {proxy} = getCurrentInstance();
const {
  sys_qh_difficulty,
  sys_qh_questions_type,
  sys_qh_paper_type,
  sys_qh_grade,
  sys_qh_subject
} = proxy.useDict("sys_qh_difficulty", "sys_qh_questions_type", "sys_qh_paper_type", "sys_qh_grade", "sys_qh_subject");
// 新增计算属性：处理试卷名称的显示
const displayPaperName = computed(() => {
  if (examInfo.paperName) {
    // 使用正则表达式去掉冒号及后面的所有内容
    return examInfo.paperName.replace(/:.*$/, '');
  }
  return examInfo.paperName;
});


// 控制悬浮按钮显示状态
const showFloatingButton = ref(true)

// 处理添加知识树
const handleAddKnowledgeTree = () => {
  if (!currentQuestion.value?.bankId) {
    ElMessage.warning('请先选择所属题库');
    return;
  }

  const knowledgeTreeId = currentQuestion.value.bankId;
  router.push("/qh/knowledgeTree-detail/index/" + knowledgeTreeId);
};

// 监听滚动事件以保持按钮位置
const handleScroll = () => {
  // 这里可以根据需要添加滚动处理逻辑
};

// 设置滚动监听
onMounted(() => {
  window.addEventListener('scroll', handleScroll);
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});

// 新增方法：当输入变化时更新原始数据
const updatePaperName = (newValue) => {
  // 保留原始的后缀（如果有）
  const originalSuffix = examInfo.paperName.includes(':')
      ? examInfo.paperName.substring(examInfo.paperName.indexOf(':'))
      : '';

  // 更新原始数据：新值 + 原始后缀
  examInfo.paperName = newValue + originalSuffix;
};
// 当前题目计算属性
const currentQuestion = computed(() => {
  if (questions.value.length > 0 && currentIndex.value < questions.value.length) {
    return questions.value[currentIndex.value]
  }
  return null
})

// 当前试题考点选项
const currentKnowledgeTreeOptions = computed(() => {
  if (!currentQuestion.value || !currentQuestion.value.bankId) {
    return [];
  }
  const bankId = currentQuestion.value.bankId;
  const node = knowledgeTreeMap.value[bankId];
  return node?.children || [];
})

// 已提交题目数量
const submittedCount = computed(() => {
  return questions.value.filter(q => q.submitted).length
})

// 已提交百分比
const submittedPercentage = computed(() => {
  if (questions.value.length === 0) return 0
  return Math.round((submittedCount.value / questions.value.length) * 100)
})

// 是否全部提交
const allSubmitted = computed(() => {
  return submittedCount.value === questions.value.length && questions.value.length > 0
})

// 自定义进度条颜色
const customColors = [
  {color: '#e6a23c', percentage: 20},
  {color: '#9c27b0', percentage: 40},
  {color: '#6f7ad3', percentage: 60},
  {color: '#1989fa', percentage: 80},
  {color: '#5cb87a', percentage: 100}
]

// 试卷信息校验规则
const examInfoRules = reactive({
  paperName: [{required: true, message: '请输入试卷名称', trigger: 'blur'}],
  paperType: [{required: true, message: '请选择试卷类型', trigger: 'change'}],
  pyear: [{required: true, message: '请选择试卷年份', trigger: 'change'}],
  region: [{required: true, type: 'array', min: 3, message: '请选择完整省市区', trigger: 'change'}],
  grade: [{required: true, message: '请选择所属年级', trigger: 'change'}],
  subject: [{required: true, message: '请选择所属学科', trigger: 'change'}]
})

// 题目校验规则 - 更新试题考点为数组校验
const questionRules = reactive({
  context: [{required: true, message: '请上传题目图片', trigger: 'change'}],
  questionType: [{required: true, message: '请选择试题类型', trigger: 'change'}],
  difficulty: [{required: true, message: '请选择试题难度', trigger: 'change'}],
  bankId: [{required: true, message: '请选择所属题库', trigger: 'change'}],
  knowledgeTreeIds: [
    {
      required: true,
      type: 'array',
      min: 1,
      message: '请至少选择一个试题考点',
      trigger: 'change'
    }
  ],
  ocrText: [{required: true, message: '请输入试题内容', trigger: 'blur'}],
})

// 验证试卷信息
const validateExamInfo = () => {
  if (examInfoRef.value) {
    examInfoRef.value.validate((valid) => {
      examInfoValid.value = valid
    })
  }
}

// 验证当前题目
const validateCurrentQuestion = () => {
  if (questionRef.value && currentQuestion.value) {
    questionRef.value.validate((valid) => {
      questionValid.value = valid
    })
  }
}

// 是否可以提交当前题目
const canSubmitQuestion = computed(() => {
  if (currentQuestion.value && currentQuestion.value.submitted) {
    return false
  }
  return examInfoValid.value && questionValid.value
})

// 获取图片URL
const getImageUrl = (path) => {
  if (path && (path.startsWith('http://') || path.startsWith('https://'))) {
    return path
  }
  return path ? import.meta.env.VITE_APP_BASE_API + path : ''
}

// 打开图片预览
const openImagePreview = (url) => {
  previewImageUrl.value = getImageUrl(url)
  imagePreviewVisible.value = true
}

// 调整预览图片大小
const adjustPreviewImage = (e) => {
  const img = e.target
  const container = img.parentElement
  const maxWidth = container.clientWidth * 0.9
  const maxHeight = container.clientHeight * 0.9
  const scale = Math.min(maxWidth / img.width, maxHeight / img.height)

  if (scale < 1) {
    img.style.width = `${img.width * scale}px`
    img.style.height = `${img.height * scale}px`
  }
}

// 处理图片上传
const handleImageUpload = async (file, type) => {
  if (!currentQuestion.value) return;

  const isImage = file.raw.type.startsWith('image/');
  if (!isImage) {
    ElMessage.error('请上传图片格式的文件');
    return;
  }

  const isLt5M = file.raw.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过5MB');
    return;
  }

  try {
    uploadLoading.value = true;
    const response = await fileUpload(file.raw);

    if (response.code === 200 && response.msg) {
      const imageUrl = response.msg;
      if (type === 'question') {
        currentQuestion.value.context = imageUrl;
      } else if (type === 'answer') {  // 新增答案图片处理
        currentQuestion.value.questionAnswer = imageUrl;
      } else if (type === 'analysis') {  // 原解析图片改为analysis
        currentQuestion.value.questionAnalyze = imageUrl;
      }
      ElMessage.success(`${type === 'question' ? '题目' : '答案'}图片上传成功`);
      validateCurrentQuestion();
    } else {
      ElMessage.error(`${type === 'question' ? '题目' : '答案'}图片上传失败: ${response.msg || '未知错误'}`);
    }
  } catch (error) {
    console.error(`${type === 'question' ? '题目' : '答案'}图片上传出错:`, error);
    ElMessage.error(`${type === 'question' ? '题目' : '答案'}图片上传失败，请重试`);
  } finally {
    uploadLoading.value = false;
  }
}

// 处理图片加载失败
const handleImageError = (type) => {
  ElMessage.warning(`${type === 'question' ? '题目' : '答案'}图片加载失败`);
}

// 切换记忆属性状态
const toggleRememberAttributes = () => {
  rememberAttributes.value = !rememberAttributes.value;
  if (rememberAttributes.value) {
    // 开启记忆时保存当前属性
    saveCurrentAttributes();
    ElMessage.success('已开启记忆属性功能');
  } else {
    ElMessage.info('已关闭记忆属性功能');
  }
}

// 保存当前题目的属性值
const saveCurrentAttributes = () => {
  if (currentQuestion.value) {
    // 更新全局记忆属性
    savedAttributes.value = {
      difficulty: currentQuestion.value.difficulty,
      bankId: currentQuestion.value.bankId,
      tag: currentQuestion.value.tag,
      knowledgeTreeIds: [...currentQuestion.value.knowledgeTreeIds] // 保存考点数组
    }

    // 按索引保存当前题目属性
    questionAttributes.value[currentIndex.value] = {
      ...savedAttributes.value
    }
  }
}

// 应用保存的属性值
const applySavedAttributes = () => {
  if (currentQuestion.value && !currentQuestion.value.submitted) {
    const currentId = currentIndex.value

    // 如果当前题目有保存的属性，则使用
    if (questionAttributes.value[currentId]) {
      Object.assign(currentQuestion.value, questionAttributes.value[currentId])
    }
    // 否则使用全局记忆属性
    else if (rememberAttributes.value) {
      Object.assign(currentQuestion.value, savedAttributes.value)
    }

    // 处理题库和考点的联动
    if (currentQuestion.value.bankId) {
      const node = knowledgeTreeMap.value[currentQuestion.value.bankId];
      if (node && (!node.children || node.children.length === 0)) {
        currentQuestion.value.knowledgeTreeIds = [];
      }
    }

    setTimeout(() => {
      validateCurrentQuestion()
    }, 100)
  }
}

// 处理题库切换
const handleBankChange = (bankId) => {
  if (currentQuestion.value) {
    currentQuestion.value.knowledgeTreeIds = [];
    const node = knowledgeTreeMap.value[bankId];
    if (node && (!node.children || node.children.length === 0)) {
      ElMessage.info('该题库下暂无考点信息');
    }
  }
}

// 提交当前题目
const submitQuestion = () => {
  if (canSubmitQuestion.value && currentQuestion.value && !currentQuestion.value.submitted) {
    // 提交前强制保存属性（无论是否开启记忆）
    saveCurrentAttributes();

    currentQuestion.value.submitted = true;
    ElMessage.success(`第 ${currentIndex.value + 1} 题提交成功！`)

    if (currentIndex.value < questions.value.length - 1) {
      setTimeout(() => {
        currentIndex.value++
        applySavedAttributes()
      }, 800)
    }
  } else {
    validateExamInfo()
    validateCurrentQuestion()

    if (!examInfoValid.value) {
      ElMessage.warning('请完善试卷信息')
    } else if (!questionValid.value) {
      ElMessage.warning('请完善题目信息')
    }
  }
}

// 提交全部 - 增加loading效果
const submitAll = async () => {
  // 开启提交loading
  submitLoading.value = true;
  try {
    // 先验证试卷信息
    examInfoRef.value.validate((valid) => {
      if (!valid) {
        ElMessage.warning('请完善试卷信息');
        submitLoading.value = false; // 关闭loading
        return;
      }

      // 检查是否所有题目都已提交
      for (let i = 0; i < questions.value.length; i++) {
        if (!questions.value[i].submitted) {
          ElMessage.warning(`请先完成第 ${i + 1} 题`);
          submitLoading.value = false; // 关闭loading
          return;
        }
      }

      // 1. 构建试卷信息（匹配后端PaperAnalysisForm）
      const paperAnalysisForm = {
        paperName: examInfo.paperName,
        paperType: examInfo.paperType,
        pyear: examInfo.pyear,
        region: examInfo.region.join(','), // 地区名称字符串
        grade: examInfo.grade,
        subject: examInfo.subject
      };

      // 2. 构建题目列表（匹配后端QuestionAnalysisForm）
      const questionAnalysisFormList = questions.value.map(q => ({
        questionType: q.questionType,
        tag: q.tag,
        context: q.context, // 题目图片url
        questionAnswer: q.questionAnswer, // 答案图片url
        questionAnalyze: q.questionAnalyze, // 解析图片url
        ocrText: q.ocrText, // 识图解析文本
        difficulty: q.difficulty, // 难度
        bankId: q.bankId, // 所属题库
        knowledgeTreeIds: q.knowledgeTreeIds // 知识点ID数组
      }));

      // 3. 构建最终提交数据（匹配后端PaperQuestionAnalysisForm）
      const submitData = {
        paperAnalysisForm: paperAnalysisForm, // 试卷信息
        questionAnalysisFormList: questionAnalysisFormList // 题目列表
      };

      // 4. 调用接口提交
      addQuestionBank(submitData).then(response => {
        if (response.code === 200) {
          questions.value.forEach(q => q.submitted = true);
          ElMessage.success('所有题目已成功提交到服务器！');
          router.push('/qh/questionEntry-index/index');
        } else {
          ElMessage.error('提交失败: ' + (response.msg || '未知错误'));
        }
        submitLoading.value = false; // 关闭loading
      }).catch(error => {
        console.error('提交全部出错:', error);
        ElMessage.error('提交过程中发生错误，请重试');
        submitLoading.value = false; // 关闭loading
      });
    });
  } catch (error) {
    console.error('提交全部出错:', error);
    ElMessage.error('提交过程中发生错误，请重试');
    submitLoading.value = false; // 关闭loading
  }
};

// 上一题
const prevQuestion = () => {
  if (currentIndex.value > 0) {
    saveCurrentAttributes()
    currentIndex.value--
    applySavedAttributes()
  }
}

// 下一题
const nextQuestion = () => {
  if (currentIndex.value < questions.value.length - 1) {
    saveCurrentAttributes()
    currentIndex.value++
    applySavedAttributes()
  }
}

// 新增题目
const addNewQuestion = () => {
  const newQuestion = {
    submitted: false,
    questionType: '',
    difficulty: '',
    knowledgeTreeIds: [], // 初始化为空数组
    bankId: '',
    tag: '',
    context: 'http://117.72.14.183:9000/qh-test/试题默认图片.png',
    questionAnswer: 'http://117.72.14.183:9000/qh-test/答案默认图片.png',
    questionAnalyze: 'http://117.72.14.183:9000/qh-test/解析默认图片.png',
    ocrText: ''
  }

  // 应用记忆属性（如果开启）
  if (rememberAttributes.value) {
    Object.assign(newQuestion, savedAttributes.value);
  }

  questions.value.push(newQuestion)
  currentIndex.value = questions.value.length - 1

  if (questionRef.value) {
    questionRef.value.clearValidate()
  }

  ElMessage.success('新增题目成功，请填写题目信息')
}

// 删除题目
const deleteQuestion = () => {
  ElMessageBox.confirm(
      `确定要删除第 ${currentIndex.value + 1} 题吗？`,
      '删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
  ).then(() => {
    const deletedIndex = currentIndex.value
    questions.value.splice(deletedIndex, 1)
    delete questionAttributes.value[deletedIndex]

    // 重新索引
    const newAttributes = {};
    Object.keys(questionAttributes.value).forEach(key => {
      const idx = parseInt(key);
      if (idx > deletedIndex) {
        newAttributes[idx - 1] = questionAttributes.value[key];
      } else if (idx < deletedIndex) {
        newAttributes[idx] = questionAttributes.value[key];
      }
    });
    questionAttributes.value = newAttributes;

    if (deletedIndex === questions.value.length) {
      currentIndex.value = deletedIndex - 1
    } else {
      currentIndex.value = deletedIndex
    }

    if (questions.value.length > 0) {
      applySavedAttributes()
    }

    ElMessage.success('题目删除成功')
  }).catch(() => {
    ElMessage.info('已取消删除')
  })
}

// 获取知识树数据
const fetchKnowledgeTree = async () => {
  try {
    const response = await selectKnowledgeTreeList();
    if (response.code === 200) {
      const data = response.data;
      knowledgeTreeList.value = data;

      const nodeMap = {};
      data.forEach(node => {
        nodeMap[node.id] = node;
      });
      knowledgeTreeMap.value = nodeMap;

      const childrenMap = {};
      data.forEach(node => {
        if (!childrenMap[node.parentId]) {
          childrenMap[node.parentId] = [];
        }
        childrenMap[node.parentId].push(node);
      });
      knowledgeTreeChildrenMap.value = childrenMap;

      data.forEach(node => {
        node.children = childrenMap[node.id] || [];
      });

      knowledgeTreeRoots.value = data.filter(node => node.parentId === '0');
    }
  } catch (error) {
    console.error('获取知识树失败:', error);
    ElMessage.error('获取知识树数据失败');
  }
};

// 解析试卷数据
const parsePaperData = (data) => {
  try {
    const paperData = JSON.parse(data.data);
    examInfo.paperName = paperData.paperName || '';
    examInfo.paperType = paperData.paperType || '';

    // 确保region是数组
    if (Array.isArray(paperData.region)) {
      examInfo.region = [...paperData.region];
    } else if (paperData.region) {
      // 如果后端返回的是字符串，尝试分割
      examInfo.region = paperData.region.split(',').filter(Boolean);
    } else {
      examInfo.region = [];
    }

    examInfo.pyear = paperData.pyear || '';
    examInfo.grade = paperData.grade || '';
    examInfo.subject = paperData.subject || '';

    questions.value = paperData.details.map(question => ({
      ...question,
      submitted: false,
      questionType: question.questionType || '',
      difficulty: question.difficulty || '',
      knowledgeTreeIds: Array.isArray(question.knowledgeTreeIds)
          ? [...question.knowledgeTreeIds]
          : (question.knowledgeTreeIds ? [question.knowledgeTreeIds] : []), // 兼容旧数据
      bankId: question.bankId || '',
      tag: question.tag || '',
      context: question.context || 'http://117.72.14.183:9000/qh-test/试题默认图片.png',
      questionAnswer: question.questionAnswer || 'http://117.72.14.183:9000/qh-test/答案默认图片.png',
      questionAnalyze: question.questionAnalyze || 'http://117.72.14.183:9000/qh-test/解析默认图片.png',
    }));

    uploadedFile.value = {
      name: route.params.fileName.replace(/:.*$/, '') || '试卷文件'
    };

    loading.value = false;

    setTimeout(() => {
      validateExamInfo()
      validateCurrentQuestion()
    }, 500)
  } catch (error) {
    console.error('解析试卷数据出错:', error);
    ElMessage.error('解析试卷数据失败，请检查数据格式');
    loading.value = false;
  }
};

// 页面加载
onMounted(async () => {
  try {
    await fetchKnowledgeTree();

    const fileName = route.params.fileName;
    if (!fileName) {
      ElMessage.error('缺少试卷文件名参数');
      loading.value = false;
      return;
    }

    const response = await uploadResult(fileName);
    if (response.code === 200) {
      parsePaperData(response);
    } else {
      ElMessage.error('获取试卷数据失败: ' + (response.msg || '未知错误'));
      loading.value = false;
    }
  } catch (error) {
    console.error('获取试卷数据出错:', error);
    ElMessage.error('获取试卷数据失败，请重试');
    loading.value = false;
  }
});

// 监听表单变化
watch(examInfo, () => {
  validateExamInfo()
}, {deep: true})

watch(currentQuestion, () => {
  validateCurrentQuestion()
}, {deep: true})

watch(currentIndex, () => {
  if (questionRef.value) {
    questionRef.value.clearValidate()
    validateCurrentQuestion()
  }
})
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 20px;
  background-color: #f8fafc;
  min-height: 100vh;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

.file-header {
  background: linear-gradient(135deg, #f6f9ff, #eef5ff);
  border-radius: 10px;
  padding: 15px 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #409eff;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #2c3e50;
}

.file-icon {
  color: #409eff;
  font-size: 20px;
}

.file-name {
  font-weight: 500;
  letter-spacing: 0.5px;
}

.content-container {
  margin-top: 20px;
}

.no-question提示 {
  text-align: center;
  padding: 50px 0;
  background-color: #fff;
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
}

.info-card, .question-card {
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  margin-bottom: 24px;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }
}

.card-header {
  padding: 16px 20px;
  background: linear-gradient(to right, #f5f7fa, #e4e7ed);
  border-bottom: 1px solid #ebeef5;
  font-weight: 600;
  color: #303133;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px 12px 0 0;
}

.question-header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.remember-btn {
  margin-left: 10px;
}

.question-counter {
  font-size: 15px;
  color: #606266;
  font-weight: 500;
  background: #f0f2f5;
  padding: 5px 12px;
  border-radius: 16px;
}

.info-form, .question-form {
  padding: 20px 25px 0px;
}

.form-row-margin {
  margin-bottom: 20px;
}

.image-container {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 15px;
  background-color: #fafcff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.image-preview {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  overflow: hidden;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  cursor: zoom-in;
  position: relative;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.image-preview:hover .preview-image {
  transform: scale(1.02);
}

.zoom-indicator {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .zoom-indicator {
  opacity: 1;
}

.placeholder {
  color: #a8abb2;
  text-align: center;
  padding: 40px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  height: 300px;
  justify-content: center;

  .el-icon {
    font-size: 48px;
    color: #c0c4cc;
  }

  span {
    font-size: 14px;
  }
}

.upload-btn {
  width: 100%;
  border-radius: 6px;
  padding: 8px 0;
}

.upload-loading, .loading-icon {
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.question-select, .question-input, .question-textarea {
  border-radius: 8px;

  :deep(.el-input__inner) {
    border-radius: 8px;
  }
}

.question-textarea {
  :deep(.el-textarea__inner) {
    border-radius: 8px;
    min-height: 100px !important;
    font-size: 14px;
    line-height: 1.6;
  }
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 20px 25px;
  border-top: 1px solid #ebeef5;
  background: #f9fbfd;
  border-radius: 0 0 12px 12px;
  gap: 15px;
  flex-wrap: wrap;
}

/* 左侧：新增和删除按钮组 */
.edit-buttons {
  display: flex;
  gap: 10px; /* 增加按钮间距 */
  margin-right: auto;
}

.add-btn, .delete-btn {
  padding: 10px 15px;
  border-radius: 8px; /* 恢复圆角 */
}

/* 右侧：录题和全部提交按钮组 */
.submit-buttons {
  display: flex;
  gap: 10px; /* 增加按钮间距 */
  margin-left: auto;
}

.submit-btn, .submit-all-btn {
  border-radius: 8px; /* 恢复圆角 */
}

/* 中间：导航按钮组 */
.navigation-buttons {
  display: flex;
  gap: 0; /* 按钮之间无间隙 */
  margin: 0 auto;
}

.nav-btn {
  padding: 10px 20px;
  border-radius: 0; /* 取消圆角，使按钮紧贴 */
  font-weight: 500;

  &:first-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
  }

  &:last-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.stats-bar {
  margin-top: 20px;
  padding: 15px 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border: 1px solid #ebeef5;
}

.progress-bar {
  margin-bottom: 15px;
  border-radius: 8px;
  overflow: hidden;
}

.stats-info {
  display: flex;
  justify-content: space-between;
  font-size: 15px;
  color: #606266;
  font-weight: 500;

  strong {
    color: #409eff;
    font-weight: 600;
  }

  .submitted strong {
    color: #67c23a;
  }

  .percentage strong {
    color: #9c27b0;
  }
}

.preview-container {
  width: 100%;
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.preview-img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  transition: all 0.3s ease;
}

:deep(.el-dialog__body) {
  padding: 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 85vh;
}

@media (max-width: 992px) {
  .action-buttons {
    justify-content: center;
  }

  .edit-buttons, .navigation-buttons, .submit-buttons {
    width: 100%;
    justify-content: center;
    margin: 0 0 10px 0;
  }

  .image-preview {
    height: 200px;
  }
}

/* 悬浮按钮样式 */
.floating-button {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;

  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #66b1ff;
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 6px 16px rgba(64, 158, 255, 0.5);
  }

  &.disabled {
    background-color: #c0c4cc;
    cursor: not-allowed;

    &:hover {
      background-color: #c0c4cc;
      transform: translateY(-50%);
      box-shadow: 0 4px 12px rgba(192, 196, 204, 0.4);
    }
  }

  .add-icon {
    color: white;
    font-size: 28px;
    font-weight: bold;
  }
}
</style>
