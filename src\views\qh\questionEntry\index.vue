<template>
  <div class="app-container">
    <!-- 上传区域 -->
    <el-card class="upload-card" shadow="never">
      <div class="upload-area">
        <el-upload
            class="upload-demo"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleUpload"
            :show-file-list="false"
            accept=".doc,.docx"
        >
          <el-icon class="el-icon--upload">
            <upload-filled/>
          </el-icon>
          <div class="el-upload__text">
            拖拽试卷文件到此处或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持上传 .doc 或 .docx 格式的试卷文件
            </div>
          </template>
        </el-upload>
      </div>
    </el-card>

    <!-- 历史记录区域 -->
    <el-card class="history-card" shadow="never">
      <!-- 历史记录列表 -->
      <div class="history-list">
        <div
            v-for="(item, index) in paginatedHistory"
            :key="item.title"
            class="history-item"
            @click="handleHistoryClick(item)"
        >
          <div class="row-number">
            {{ (currentPage - 1) * pageSize + index + 1 }}
          </div>
          <div class="file-name">{{ formatTitle(item.title) }}</div>
          <div class="right-group">
            <span class="upload-time">{{ item.uploadTime }}</span>
            <span class="status-tag" :class="getStatusClass(item.status)">
          {{ getStatusText(item.status) }}
        </span>
            <!-- 添加下载和删除按钮 -->
            <div class="action-buttons">
              <el-button
                  type="primary"
                  size="default"
                  @click.stop="handleDownload(item)"
              >
                <el-icon>
                  <Download/>
                </el-icon>
                下载
              </el-button>
              <el-button
                  type="danger"
                  size="default"
                  @click.stop="handleDelete(item)"
              >
                <el-icon>
                  <Delete/>
                </el-icon>
                删除
              </el-button>
            </div>
            <el-icon class="arrow-icon">
              <arrow-right/>
            </el-icon>
          </div>
        </div>
        <div class="empty-state" v-if="paginatedHistory.length === 0">
          <el-empty description="暂无上传记录"/>
        </div>
      </div>
    </el-card>

    <!-- 分页控件移到外部 -->
    <div class="pagination-wrapper">
      <el-pagination
          class="pagination-control"
          :current-page="currentPage"
          :page-size="pageSize"
          :total="filteredHistory.length"
          :page-sizes="pageSizes"
          layout="sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script setup>
// 新增图标导入
import {ArrowRight, Delete, Download, UploadFilled} from '@element-plus/icons-vue'
import {computed, getCurrentInstance, onMounted, onUnmounted, ref} from 'vue'
import {ElEmpty, ElMessage, ElMessageBox} from 'element-plus'
import {useRouter} from 'vue-router'
// 导入后端接口
import {uploadDelete, uploadPaperFile, uploadStatus} from "@/api/qh/paperUpload.js";

const router = useRouter()

// 历史记录相关
const searchKeyword = ref('')
const historyRecords = ref([]) // 现在存储数组形式的历史记录
const refreshTimer = ref(null) // 定时器引用
// 存储上传的文件信息
const uploadedFile = ref(null)

const {proxy} = getCurrentInstance();
const {
  sys_qh_difficulty,
  sys_qh_questions_type,
  sys_qh_paper_type
} = proxy.useDict("sys_qh_difficulty", "sys_qh_questions_type", "sys_qh_paper_type");

// 分页相关变量
const pageSizes = [10, 20, 50];
const currentPage = ref(1);
const pageSize = ref(10);

// 分页处理后的历史记录
const paginatedHistory = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredHistory.value.slice(start, end);
});

// 处理每页条数变化
const handleSizeChange = (newSize) => {
  pageSize.value = newSize;
  currentPage.value = 1; // 重置到第一页
};

// 处理页码变化
const handlePageChange = (newPage) => {
  currentPage.value = newPage;
};

// 过滤后的历史记录
const filteredHistory = computed(() => {
  if (!searchKeyword.value) return historyRecords.value

  return historyRecords.value.filter(item =>
      item.title.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'processing':
      return '正在解析'
    case 'success':
      return '等待录入'
    case 'finished':
      return '录入完成'
    case 'error':
      return '解析失败'
    default:
      return '未知状态'
  }
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'processing':
      return 'processing-tag'
    case 'success':
      return 'success-tag'
    case 'finished':
      return 'finished-tag'
    case 'error':
      return 'error-tag'
    default:
      return ''
  }
}

// 格式化标题：移除最后一个冒号及后面的内容
const formatTitle = (title) => {
  if (!title) return '';
  // 查找最后一个冒号的位置
  const lastColonIndex = title.lastIndexOf(':');
  // 如果存在冒号，截取冒号前的部分；否则返回原始标题
  return lastColonIndex > -1 ? title.substring(0, lastColonIndex) : title;
};

// 点击历史记录项跳转
const handleHistoryClick = (item) => {
  // 检查状态是否为"解析成功"
  if (item.status === 'processing') {
    ElMessage.error('试卷解析中，请耐心等待！');
    return;
  }

  if (item.status === 'error') {
    ElMessage.error('试卷解析失败，请重试！');
    return;
  }

  // 状态为"解析成功"时才进行跳转
  router.push("/qh/questionEntry-detail/index/" + item.title);
}

// 获取历史记录函数
const fetchHistory = async () => {
  try {
    const response = await uploadStatus()
    if (response.code === 200) {
      // 直接使用后端返回的数组数据
      historyRecords.value = response.data
    } else {
      ElMessage.error('获取历史记录失败')
    }
  } catch (error) {
    console.error('获取历史记录出错:', error)
  }
}

// 处理文件上传 - 异步调用后端接口
const handleUpload = async (file) => {
  // 保存上传的文件信息（前端临时URL）
  uploadedFile.value = {
    name: file.name,
    url: URL.createObjectURL(file.raw),
    type: file.type
  }

  // 显示提交成功消息
  ElMessage.success('已提交解析，请等待处理完成')

  try {
    // 调用后端接口
    const response = await uploadPaperFile(file.raw)

    // 立即刷新历史记录
    await fetchHistory()

  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error('上传失败，请检查网络或联系管理员')
  }
}

// 处理下载
const handleDownload = (item) => {
  // 检查是否有有效的下载URL
  if (!item.url) {
    ElMessage.error('下载链接无效')
    return
  }

  // 创建隐藏的下载链接
  const link = document.createElement('a')
  link.href = item.url
  // 设置下载文件名
  link.download = `${item.title}.docx` // 可以根据实际格式调整
  link.style.display = 'none'

  // 添加到DOM并触发点击
  document.body.appendChild(link)
  link.click()

  // 清理
  setTimeout(() => {
    document.body.removeChild(link)
  }, 100)
};

// 处理删除
const handleDelete = (item) => {
  ElMessageBox.confirm(`确定要删除试卷 "${formatTitle(item.title)}" 的解析记录吗？`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 调用删除接口
      const response = await uploadDelete(item.title);
      if (response.code === 200) {
        ElMessage.success('删除成功');
        // 刷新历史记录
        await fetchHistory();
      } else {
        ElMessage.error('删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      ElMessage.error('删除失败');
    }
  }).catch(() => {
  });
};

// 页面加载时获取历史记录并设置定时器
onMounted(() => {
  // 初始加载
  fetchHistory()

  // 设置定时器，每2秒刷新一次
  refreshTimer.value = setInterval(() => {
    fetchHistory()
  }, 2000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
})
</script>

<style scoped lang="scss">
.app-container {
  position: relative;
  padding: 20px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

// 历史记录样式
.history-card {
  margin-bottom: 20px;
}

.history-header {
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 16px;
}

.search-input {
  margin-bottom: 15px;
}

.history-list {
  margin-top: 10px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 10px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f5f7fa;
  }
}

.file-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 10px;
}

.right-group {
  display: flex;
  align-items: center;
  gap: 15px; /* 增加元素间距 */
}

.upload-time {
  color: #606266;
  font-size: 13px;
  white-space: nowrap;
}

.status-tag {
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
  white-space: nowrap;
}

.processing-tag {
  background-color: #e6f7ff;
  color: #1890ff;
}

.success-tag {
  background-color: #f0f9eb;
  color: #52c41a;
}

.finished-tag {
  background-color: #f0f9eb;
  color: #052df1;
}

.error-tag {
  background-color: #fff2f0;
  color: #f5222d;
}

.arrow-icon {
  color: #c0c4cc;
  margin-left: 5px;
}

.empty-state {
  margin: 50px 0;
  text-align: center;
}

.upload-card {
  margin-bottom: 20px;
  border-radius: 4px;
}

.upload-area {
  padding: 0px 0;
  text-align: center;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
  transition: border-color 0.3s;

  &:hover {
    border-color: #409eff;
  }
}

.el-icon--upload {
  font-size: 60px;
  color: #8c939d;
  margin-bottom: 20px;
}

.el-upload__text {
  font-size: 16px;
  color: #606266;

  em {
    color: #409eff;
    font-style: normal;
  }
}

.el-upload__tip {
  color: #8c939d;
  margin-top: 10px;
  margin-bottom: 10px;
}

.file-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 10px 15px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.file-icon {
  color: #409eff;
}

.row-number {
  width: 40px;
  text-align: center;
  font-weight: bold;
  color: #666;
  margin-right: 10px;
}

.pagination-control {
  margin-top: 20px; /* 与上部分内容保持一定间距 */
  text-align: center; /* 水平居中 */
  width: 100%; /* 占据父容器全部宽度 */
  font-size: 14px; /* 增大默认字体大小 */
}

.pagination-control .el-pagination__sizes {
  margin-right: 10px; /* 每页数量选择器与分页器之间的间距 */
}

.pagination-control .el-select .el-input {
  font-size: 14px; /* 每页数量选择器字体大小 */
}

.pagination-control .el-pagination__jump {
  margin-left: 10px; /* 跳转页码区域与分页器间距 */
}
</style>
