<template>
  <div class="paper-detail-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-content">
        <el-icon class="loading-icon" size="40">
          <Loading />
        </el-icon>
        <div class="loading-text">正在加载试卷详情...</div>
      </div>
    </div>

    <div v-else>
      <!-- 试卷标题 -->
      <div class="paper-title">
        <h2>{{ paperName || '试卷详情' }}</h2>
      </div>

      <!-- 试卷信息 -->
      <div class="paper-info">
        <span>总分：{{ totalScore }}分</span>
        <span class="paper-info-item">题目数量：{{ paperData.length }}道</span>
      </div>

      <el-divider></el-divider>

      <!-- 试卷内容 -->
      <div class="paper-content">
        <!-- 动态生成各种题型部分 -->
        <div v-for="type in questionGroups.typeOrder" :key="type" class="question-section">
          <div class="section-title">{{ getQuestionTypeName(type) }}</div>
          <div class="question-list">
            <div v-for="(question, index) in questionGroups.groups[type]" :key="question.id" class="question-item">
              <!-- 题目元信息区域 - 放在题目上方 -->
              <div class="meta-info">
                <!-- 题型和难度 -->
                <div class="info-row">
                  <dict-tag
                    :options="sys_qh_questions_type"
                    :value="question.questionType"
                    class="type-tag unified-tag"
                  />
                  <div class="info-item">
                    <span class="item-label">难度:</span>
                    <span class="difficulty-value">{{ getDifficultyLabel(question.difficulty) }}</span>
                  </div>

                  <!-- 知识点 -->
                  <div v-if="getKnowledgeDisplay(question)" class="info-item knowledge">
                    <span class="item-label">知识点:</span>
                    <div class="knowledge-list">
                      <span
                        v-for="(item, idx) in getKnowledgeDisplay(question)"
                        :key="idx"
                        class="knowledge-item"
                      >
                        {{ item }}
                        <span
                          v-if="idx < getKnowledgeDisplay(question).length - 1"
                          class="separator"
                        >/</span>
                      </span>
                    </div>
                  </div>

                  <!-- 分值 -->
                  <div class="info-item score">
                    <span class="item-label">分值:</span>
                    <span class="score-value">{{ question.score }}分</span>
                  </div>
                </div>
              </div>
              
              <!-- 题目内容 -->
              <div class="question-header">
                <span class="question-num">{{ getQuestionNumber(type, index) }}.</span>
                <div class="question-content">
                  <img :src="question.context" class="question-image"/>
                </div>
              </div>
              
              <!-- 来源信息 -->
              <div class="answer-control">
                <span v-if="question.sourcePaper || question.region || question.year" class="question-source">
                  <span v-if="question.sourcePaper" class="source-info-item">来源：{{ question.sourcePaper }}</span>
                  <span v-if="question.region" class="source-info-item">地区：{{ question.region }}</span>
                  <span v-if="question.year" class="source-info-item">年份：{{ question.year }}</span>
                </span>
                
                <el-button class="analysis-btn" icon="View" plain size="small" type="primary"
                           @click="toggleAnalysis(question.id)">
                  {{ showAnalysisId === question.id ? '收起解析' : '展开解析' }}
                </el-button>
              </div>
              
              <div v-if="showAnalysisId === question.id" class="question-analysis">
                <div class="analysis-content">
                  <img :src="question.questionAnalyze" class="analysis-image"/>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {computed, getCurrentInstance, onMounted, onBeforeUnmount, ref, watch} from 'vue';
import {getPaperInfo} from '@/api/qh/paper';
import {Loading} from '@element-plus/icons-vue';

const {proxy} = getCurrentInstance();
// 使用系统字典
const {sys_qh_questions_type, sys_qh_difficulty} = proxy.useDict("sys_qh_questions_type", "sys_qh_difficulty");

const props = defineProps({
  paperId: {
    type: [Number, String],
    required: true
  },
  paperName: {
    type: String,
    default: ''
  },
  flag: {
    type: String,
    default: 'SJ'
  }
});

const emit = defineEmits(['close']);

// 控制解析显示
const showAnalysisId = ref(null);

// 试卷数据
const paperData = ref([]);

// 加载状态
const loading = ref(false);

// 计算总分
const totalScore = computed(() => {
  return paperData.value.reduce((total, item) => {
    return total + Number(item.score || 0);
  }, 0);
});

// 按题型分组题目，保持后端返回的原始顺序
const questionGroups = computed(() => {
  const groups = {};
  const typeOrder = []; // 记录题型出现的顺序
  
  // 先遍历一遍，记录题型出现的顺序
  paperData.value.forEach(question => {
    const type = question.questionType;
    if (!typeOrder.includes(type)) {
      typeOrder.push(type);
    }
  });
  
  // 再遍历一遍，按题型分组
  paperData.value.forEach(question => {
    const type = question.questionType;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(question);
  });
  
  // 返回包含顺序信息的对象
  return {
    groups: groups,
    typeOrder: typeOrder
  };
});

// 获取问题类型名称
const getQuestionTypeName = (type) => {
  // 从系统字典中查找匹配的题型
  const typeItem = sys_qh_questions_type.value.find(item => item.value === type);
  return typeItem ? typeItem.label : `题型${type}`;
};

// 获取难度标签
const getDifficultyLabel = (difficulty) => {
  const diffItem = sys_qh_difficulty.value.find(item => item.value === difficulty);
  return diffItem ? diffItem.label : '未知难度';
};

// 生成题号
const getQuestionNumber = (type, index) => {
  let startNumber = 1;

  // 按照后端返回的顺序查找在当前题型之前的所有题目数量
  const types = questionGroups.value.typeOrder;
  const currentTypeIndex = types.indexOf(type);

  for (let i = 0; i < currentTypeIndex; i++) {
    startNumber += questionGroups.value.groups[types[i]].length;
  }

  return startNumber + index;
};

// 切换解析显示状态
const toggleAnalysis = (id) => {
  if (showAnalysisId.value === id) {
    showAnalysisId.value = null;
  } else {
    showAnalysisId.value = id;
  }
};

// 获取知识点显示数据
const getKnowledgeDisplay = (question) => {
  // 情况1：有knowledgeTreeList且包含name字段
  if (question.knowledgeTreeList && Array.isArray(question.knowledgeTreeList) && question.knowledgeTreeList.length > 0) {
    console.log('知识点数据结构:', question.knowledgeTreeList);
    
    const knowledgeNames = question.knowledgeTreeList
      .map(item => {
        // 如果有层级关系，展示完整路径
        if (item.path) {
          return item.path;
        }
        // 如果有name，直接使用
        if (item.name) {
          return item.name;
        }
        // 如果有label，使用label
        if (item.label) {
          return item.label;
        }
        // 其他情况返回null
        return null;
      })
      .filter(name => name && name !== 'undefined' && name !== 'null');
    
    console.log('处理后的知识点名称:', knowledgeNames);
    return knowledgeNames.length > 0 ? knowledgeNames : null;
  }
  
  // 情况2：有knowledgeTreeIds（只有ID数组）- 这种情况不处理，因为没有name信息
  if (question.knowledgeTreeIds && Array.isArray(question.knowledgeTreeIds) && question.knowledgeTreeIds.length > 0) {
    console.warn('题目只有知识点ID，无法显示知识点名称:', question.knowledgeTreeIds);
    return null;
  }
  
  // 情况3：单个知识点ID - 这种情况不处理，因为没有name信息
  if (question.knowledgeTreeId) {
    console.warn('题目只有单个知识点ID，无法显示知识点名称:', question.knowledgeTreeId);
    return null;
  }
  
  return null; // 表示没有知识点信息
};

// 获取试卷详情
const getPaperDetail = async (id) => {
  if (!id) {
    console.warn('paperId is empty, skipping API call');
    proxy.$modal.msgError('试卷ID不能为空');
    return;
  }
  
  // 设置加载状态为 true
  loading.value = true;
  // 重置数据
  paperData.value = [];

  try {
    console.log('开始获取试卷详情，ID:', id, 'Flag:', props.flag);
    
    const res = await getPaperInfo(id, props.flag);
    console.log('API响应:', res);
    
    if (res.code === 200) {
      paperData.value = res.data || [];
      console.log('试卷数据加载成功，题目数量:', paperData.value.length);
      
      // 验证数据完整性
      if (paperData.value.length === 0) {
        console.warn('试卷数据为空');
        proxy.$modal.msgWarning('该试卷暂无题目数据');
      }
    } else {
      console.error('API返回错误:', res.msg);
      // 接口返回错误，确保数据为空
      paperData.value = [];
      proxy.$modal.msgError(res.msg || '获取试卷详情失败');
    }
  } catch (error) {
    console.error('获取试卷详情失败', error);
    // 发生错误时清空数据
    paperData.value = [];
    
    // 根据错误类型显示不同的错误信息
    if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status;
      if (status === 404) {
        proxy.$modal.msgError('试卷不存在或已被删除');
      } else if (status === 403) {
        proxy.$modal.msgError('没有权限查看该试卷');
      } else if (status >= 500) {
        proxy.$modal.msgError('服务器内部错误，请稍后重试');
      } else {
        proxy.$modal.msgError(`请求失败 (${status})`);
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      proxy.$modal.msgError('网络连接失败，请检查网络设置');
    } else {
      // 其他错误
      proxy.$modal.msgError('获取试卷详情失败，请稍后重试');
    }
  } finally {
    // 无论成功还是失败，都将加载状态设置为 false
    loading.value = false;
  }
};

// 监听 paperId 变化，重新获取数据
watch(() => props.paperId, (newId, oldId) => {
  console.log('PaperId变化:', oldId, '->', newId);
  console.log('当前组件状态 - loading:', loading.value, 'paperData长度:', paperData.value.length);
  
  if (newId) {
    console.log('检测到paperId变化，开始获取新数据');
    getPaperDetail(newId);
  } else {
    console.warn('paperId为空，清空数据');
    paperData.value = [];
    loading.value = false;
  }
}, { immediate: true });

onMounted(() => {
  console.log('组件挂载完成，paperId:', props.paperId);
  console.log('组件挂载时状态 - loading:', loading.value, 'paperData长度:', paperData.value.length);
});

onBeforeUnmount(() => {
  console.log('组件即将销毁，清理状态');
  // 清理状态
  paperData.value = [];
  loading.value = false;
  showAnalysisId.value = null;
});
</script>

<style scoped>
.paper-detail-container {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.loading-container {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  background-color: #fafafa;
  border-radius: 8px;
  width: 100%;
  max-width: 400px;
}

.loading-icon {
  color: #409eff;
  margin-bottom: 15px;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: #606266;
  font-weight: 500;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.paper-title {
  text-align: center;
  margin-bottom: 10px;
}

.paper-title h2 {
  font-size: 22px;
  font-weight: bold;
  color: #333;
  margin: 0;
}

.paper-info {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin-bottom: 20px;
}

.paper-info-item {
  margin-left: 15px;
}

.paper-content {
  margin-top: 20px;
}

.question-section {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 4px solid #409eff;
}

.question-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.question-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #fff;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

.meta-info {
  display: flex;
  flex-direction: column;
  background: #f8fbff;
  border-radius: 6px 6px 0 0;
  padding: 12px 15px;
  margin-bottom: 0;
  border-bottom: 1px solid #ebeef5;
}

.info-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin: 0;
  padding: 0;
  min-height: 28px;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  height: 24px;
  flex-shrink: 0;
}

.info-item.knowledge {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  margin-right: 0;
}

.info-item.score {
  margin-left: auto;
  flex-shrink: 0;
}

.item-label {
  color: #666;
  margin-right: 4px;
  display: inline-flex;
  align-items: center;
  font-size: 13px;
  line-height: 1;
  white-space: nowrap;
}

.type-tag {
  display: flex;
  align-items: center;
  height: 24px;
  flex-shrink: 0;
}

.unified-tag {
  font-size: 14px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
  color: #409eff;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  height: 24px;
  box-sizing: border-box;
}

.difficulty-value {
  color: #909399;
  background-color: #f4f4f5;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  height: 20px;
  box-sizing: border-box;
}

.knowledge-list {
  display: inline-flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-wrap: nowrap;
  height: 20px;
}

.knowledge-item {
  color: #409eff;
  white-space: nowrap;
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 3px;
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
  font-weight: 500;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  flex-shrink: 0;
  height: 20px;
  box-sizing: border-box;
}

.separator {
  color: #c0c4cc;
  margin: 0 4px;
  font-weight: normal;
  line-height: 1;
}

.score-value {
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #fbc4c4;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  height: 20px;
  box-sizing: border-box;
}

.question-header {
  display: flex;
  margin-bottom: 15px;
  padding: 15px;
}

.question-num {
  font-weight: bold;
  margin-right: 10px;
  flex-shrink: 0;
}

.question-content {
  flex: 1;
}

.question-image, .analysis-image {
  max-width: 100%;
  border-radius: 4px;
  pointer-events: none;
  user-select: none;
  display: block;
}

.answer-control {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  padding: 10px 15px;
  border-top: 1px solid #ebeef5;
  background-color: #fafafa;
  border-radius: 0 0 6px 6px;
}

.question-source {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #909399;
  gap: 15px;
}

.source-info-item {
  color: #67c23a;
  background-color: #f0f9eb;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.analysis-btn {
  margin-left: auto;
}

.question-analysis {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.analysis-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
}
</style>
