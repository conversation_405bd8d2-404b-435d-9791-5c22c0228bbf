<template>
  <div class="app-container qh-workspace-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">数据入库工具</span>
          <span class="subtitle">(请按顺序填写：题库信息→年级信息→章节信息→知识点信息→题目信息)</span>
        </div>
      </template>

      <el-form :model="formData" label-width="120px">
        <!-- 所有内容在一个页面，使用折叠面板 -->
        <el-collapse v-model="activeNames">
          <!-- 题库基本信息 -->
          <el-collapse-item name="basicInfo" title="1. 题库基本信息">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="题库ID" required>
                  <el-input v-model="formData.libraryId" placeholder="请输入题库ID"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="题库名称" required>
                  <el-input v-model="formData.libraryName" placeholder="请输入题库名称"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="题库描述" required>
                  <el-input v-model="formData.description" placeholder="请输入题库描述" type="textarea"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="创建时间" required>
                  <el-date-picker v-model="formData.createTime" format="YYYY-MM-DD HH:mm:ss" placeholder="选择创建时间"
                                  type="datetime" value-format="YYYY-MM-DD HH:mm:ss"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="创建人" required>
                  <el-input v-model="formData.userId" placeholder="请输入创建人"/>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="存储类型" required>
                  <el-checkbox v-model="formData.isLocal">本地存储</el-checkbox>
                  <el-checkbox v-model="formData.isCloud">云端存储</el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="权限设置" required>
                  <el-checkbox v-model="formData.isPrivate">私有</el-checkbox>
                  <el-checkbox v-model="formData.isPublic">公开</el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <div class="form-actions">
              <el-button type="primary" @click="validateBasicInfo">下一步: 年级信息</el-button>
            </div>
          </el-collapse-item>

          <!-- 年级信息 -->
          <el-collapse-item :disabled="!isBasicInfoValid" name="gradeInfo" title="2. 年级信息">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="年级ID" required>
                  <el-input v-model="formData.gradeInputDTO.gradeId" placeholder="请输入年级ID"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="年级名称" required>
                  <el-input v-model="formData.gradeInputDTO.gradeName" placeholder="请输入年级名称"/>
                </el-form-item>
              </el-col>
            </el-row>

            <div class="form-actions">
              <el-button @click="backToBasicInfo">返回上一步</el-button>
              <el-button type="primary" @click="validateGradeInfo">下一步: 章节信息</el-button>
            </div>
          </el-collapse-item>

          <!-- 章节信息 -->
          <el-collapse-item :disabled="!isGradeInfoValid" name="chapterInfo" title="3. 章节和子章节信息">
            <div class="step-info">
              <el-alert :closable="false" show-icon type="info">
                请先添加主章节，然后可以为主章节添加子章节
              </el-alert>
            </div>

            <div class="action-bar">
              <el-button size="small" type="primary" @click="addChapter(null)">添加主章节</el-button>
            </div>

            <!-- 章节列表 -->
            <el-table
                :data="formData.gradeInputDTO.chapterInputDTOList"
                :row-class-name="chapterRowClassName"
                border
                style="width: 100%; margin-top: 15px;"
            >
              <el-table-column label="年级ID" prop="gradeId" width="200">
                <template #default="scope">
                  {{ formData.gradeInputDTO.gradeId }}
                </template>
              </el-table-column>
              <el-table-column label="章节ID" prop="chapterId" width="300"/>
              <el-table-column label="章节名称" prop="chapterName" width="200"/>
              <el-table-column label="父章节ID" prop="parentChapterId" width="200">
                <template #default="scope">
                  {{ scope.row.parentChapterId || '0' }}
                </template>
              </el-table-column>
              <el-table-column label="子章节数量" width="200">
                <template #default="scope">
                  {{ scope.row.childrenList ? scope.row.childrenList.length : 0 }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="400">
                <template #default="scope">
                  <el-button-group>
                    <el-button size="small" type="primary" @click="editChapter(scope.row, scope.$index)">编辑
                    </el-button>
                    <el-button size="small" type="success" @click="addChapter(scope.row.chapterId)">添加子项</el-button>
                    <el-button size="small" type="info" @click="viewSubChapters(scope.row)">查看子项</el-button>
                    <el-button size="small" type="danger" @click="deleteChapter(scope.row, scope.$index)">删除
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>

            <div class="form-actions">
              <el-button @click="backToGradeInfo">返回上一步</el-button>
              <el-button type="primary" @click="validateChapterInfo">下一步: 知识点信息</el-button>
            </div>
          </el-collapse-item>

          <!-- 知识点信息 -->
          <el-collapse-item :disabled="!isChapterInfoValid" name="knowledgeInfo" title="4. 知识点和子知识点信息">
            <div class="step-info">
              <el-alert :closable="false" show-icon type="info">
                请先添加主知识点，然后可以为主知识点添加子知识点
              </el-alert>
            </div>

            <div class="action-bar">
              <el-button size="small" type="primary" @click="addKnowledge(null)">添加主知识点</el-button>
            </div>

            <!-- 知识点列表 -->
            <el-table
                :data="formData.knowledgeInputDTOList"
                :row-class-name="knowledgeRowClassName"
                border
                style="width: 100%; margin-top: 15px;"
            >
              <el-table-column label="知识点ID" prop="knowledgePointId" width="300"/>
              <el-table-column label="知识点名称" prop="knowledgePointName" width="200"/>
              <el-table-column label="所属章节ID" prop="chapterId" width="200"/>
              <el-table-column label="父知识点ID" prop="parentKnowledgePointId" width="200">
                <template #default="scope">
                  {{ scope.row.parentKnowledgePointId || '0' }}
                </template>
              </el-table-column>
              <el-table-column label="子知识点数量" width="200">
                <template #default="scope">
                  {{ scope.row.childrenList ? scope.row.childrenList.length : 0 }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="400">
                <template #default="scope">
                  <el-button-group>
                    <el-button size="small" type="primary" @click="editKnowledge(scope.row, scope.$index)">编辑
                    </el-button>
                    <el-button size="small" type="success" @click="addKnowledge(scope.row.knowledgePointId)">添加子项
                    </el-button>
                    <el-button size="small" type="info" @click="viewSubKnowledge(scope.row)">查看子项</el-button>
                    <el-button size="small" type="danger" @click="deleteKnowledge(scope.row, scope.$index)">删除
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>

            <div class="form-actions">
              <el-button @click="backToChapterInfo">返回上一步</el-button>
              <el-button type="primary" @click="validateKnowledgeInfo">下一步: 试题信息</el-button>
            </div>
          </el-collapse-item>

          <!-- 试题信息 -->
          <el-collapse-item :disabled="!isKnowledgeInfoValid" name="questionInfo" title="5. 试题信息">
            <div class="step-info">
              <el-alert :closable="false" show-icon type="info">
                添加试题并关联知识点
              </el-alert>
            </div>

            <div class="action-bar">
              <el-button size="small" type="primary" @click="addQuestion">添加试题</el-button>
            </div>

            <!-- 试题列表 -->
            <el-table :data="formData.questionInputDTOList" border style="width: 100%; margin-top: 15px;">
              <el-table-column label="试题ID" prop="questionId" width="100"/>
              <el-table-column label="试题内容" prop="questionText" show-overflow-tooltip width="150"/>
              <el-table-column label="题目图片" width="150">
                <template #default="scope">
                  <el-image
                      v-if="scope.row.questionImage"
                      :preview-src-list="[scope.row.questionImage]"
                      :src="scope.row.questionImage"
                      fit="contain"
                      style="max-height: 80px; max-width: 120px"
                  />
                  <span v-else>无图片</span>
                </template>
              </el-table-column>
              <el-table-column label="答案图片" width="150">
                <template #default="scope">
                  <el-image
                      v-if="scope.row.answerImage"
                      :preview-src-list="[scope.row.answerImage]"
                      :src="scope.row.answerImage"
                      fit="contain"
                      style="max-height: 80px; max-width: 120px"
                  />
                  <span v-else>无图片</span>
                </template>
              </el-table-column>
              <el-table-column label="解析图片" width="150">
                <template #default="scope">
                  <el-image
                      v-if="scope.row.analysisImage"
                      :preview-src-list="[scope.row.analysisImage]"
                      :src="scope.row.analysisImage"
                      fit="contain"
                      style="max-height: 80px; max-width: 120px"
                  />
                  <span v-else>无图片</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="400">
                <template #default="scope">
                  <el-button-group>
                    <el-button size="small" type="primary" @click="editQuestion(scope.row, scope.$index)">编辑
                    </el-button>
                    <el-button size="small" type="success" @click="addQuestionKnowledgeRelation(scope.row.questionId)">
                      添加关联
                    </el-button>
                    <el-button size="small" type="danger" @click="deleteQuestion(scope.row, scope.$index)">删除
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>

            <div class="form-actions">
              <el-button @click="backToKnowledgeInfo">返回上一步</el-button>
              <el-button type="primary" @click="saveFormData">保存数据</el-button>
              <el-button type="success" @click="inStorage">题库入库</el-button>
              <el-button @click="resetForm">重置表单</el-button>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </el-card>

    <!-- 章节编辑对话框 -->
    <el-dialog
        v-model="chapterDialog.visible"
        :title="chapterDialog.isEdit ? '编辑章节' : '添加章节'"
        destroy-on-close
        width="50%"
    >
      <el-form :model="chapterDialog.form" label-width="120px">
        <el-form-item label="年级ID">
          <el-input v-model="chapterDialog.form.gradeId" disabled/>
        </el-form-item>
        <el-form-item v-if="chapterDialog.isEdit" label="章节ID">
          <el-input v-model="chapterDialog.form.chapterId" disabled/>
        </el-form-item>
        <el-form-item label="章节名称">
          <el-input v-model="chapterDialog.form.chapterName" placeholder="请输入章节名称"/>
        </el-form-item>
        <el-form-item v-if="chapterDialog.form.parentChapterId !== '0'" label="父章节">
          <div class="parent-info">
            <span class="label">ID:</span>
            <el-tag>{{ chapterDialog.form.parentChapterId }}</el-tag>
            <span v-if="chapterDialog.parentName" class="label">名称:</span>
            <el-tag v-if="chapterDialog.parentName" type="success">{{ chapterDialog.parentName }}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="层级类型">
          <el-tag v-if="chapterDialog.form.parentChapterId === '0'" type="warning">主章节</el-tag>
          <el-tag v-else type="info">子章节</el-tag>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="chapterDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveChapter">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 知识点编辑对话框 -->
    <el-dialog
        v-model="knowledgeDialog.visible"
        :title="knowledgeDialog.isEdit ? '编辑知识点' : '添加知识点'"
        destroy-on-close
        width="50%"
    >
      <el-form :model="knowledgeDialog.form" label-width="120px">
        <el-form-item v-if="knowledgeDialog.isEdit" label="知识点ID">
          <el-input v-model="knowledgeDialog.form.knowledgePointId" disabled/>
        </el-form-item>
        <el-form-item label="知识点名称">
          <el-input v-model="knowledgeDialog.form.knowledgePointName" placeholder="请输入知识点名称"/>
        </el-form-item>
        <el-form-item label="所属章节ID">
          <el-select v-model="knowledgeDialog.form.chapterId" placeholder="请选择所属章节" style="width: 100%">
            <el-option
                v-for="chapter in allChapters"
                :key="chapter.chapterId"
                :label="`${chapter.chapterName} (${chapter.chapterId})`"
                :value="chapter.chapterId"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="knowledgeDialog.form.parentKnowledgePointId !== '0'" label="父知识点">
          <div class="parent-info">
            <span class="label">ID:</span>
            <el-tag>{{ knowledgeDialog.form.parentKnowledgePointId }}</el-tag>
            <span v-if="knowledgeDialog.parentName" class="label">名称:</span>
            <el-tag v-if="knowledgeDialog.parentName" type="success">{{ knowledgeDialog.parentName }}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="层级类型">
          <el-tag v-if="knowledgeDialog.form.parentKnowledgePointId === '0'" type="warning">主知识点</el-tag>
          <el-tag v-else type="info">子知识点</el-tag>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="knowledgeDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveKnowledge">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 试题编辑对话框 -->
    <el-dialog
        v-model="questionDialog.visible"
        :title="questionDialog.isEdit ? '编辑试题' : '添加试题'"
        destroy-on-close
        width="70%"
    >
      <el-form :model="questionDialog.form" label-width="120px">
        <el-form-item v-if="questionDialog.isEdit" label="试题ID">
          <el-input v-model="questionDialog.form.questionId" disabled/>
        </el-form-item>
        <el-form-item label="试题内容">
          <el-input v-model="questionDialog.form.questionText" :rows="4" placeholder="请输入试题内容" type="textarea"/>
        </el-form-item>

        <!-- 题目图片上传 -->
        <el-form-item label="题目图片">
          <div class="image-upload-container">
            <div v-if="questionDialog.form.questionImage" class="image-preview">
              <el-image
                  :src="questionDialog.form.questionImage"
                  fit="contain"
                  style="max-height: 150px; max-width: 300px"
              />
              <div class="image-actions">
                <el-button size="small" type="danger" @click="questionDialog.form.questionImage = ''">删除</el-button>
              </div>
            </div>
            <el-upload
                v-else
                :before-upload="(file) => beforeImageUpload(file, 'questionImage')"
                :show-file-list="false"
                action="#"
                class="image-uploader"
            >
              <el-button type="primary">上传题目图片</el-button>
            </el-upload>
          </div>
        </el-form-item>

        <!-- 答案图片上传 -->
        <el-form-item label="答案图片">
          <div class="image-upload-container">
            <div v-if="questionDialog.form.answerImage" class="image-preview">
              <el-image
                  :src="questionDialog.form.answerImage"
                  fit="contain"
                  style="max-height: 150px; max-width: 300px"
              />
              <div class="image-actions">
                <el-button size="small" type="danger" @click="questionDialog.form.answerImage = ''">删除</el-button>
              </div>
            </div>
            <el-upload
                v-else
                :before-upload="(file) => beforeImageUpload(file, 'answerImage')"
                :show-file-list="false"
                action="#"
                class="image-uploader"
            >
              <el-button type="primary">上传答案图片</el-button>
            </el-upload>
          </div>
        </el-form-item>

        <!-- 解析图片上传 -->
        <el-form-item label="解析图片">
          <div class="image-upload-container">
            <div v-if="questionDialog.form.analysisImage" class="image-preview">
              <el-image
                  :src="questionDialog.form.analysisImage"
                  fit="contain"
                  style="max-height: 150px; max-width: 300px"
              />
              <div class="image-actions">
                <el-button size="small" type="danger" @click="questionDialog.form.analysisImage = ''">删除</el-button>
              </div>
            </div>
            <el-upload
                v-else
                :before-upload="(file) => beforeImageUpload(file, 'analysisImage')"
                :show-file-list="false"
                action="#"
                class="image-uploader"
            >
              <el-button type="primary">上传解析图片</el-button>
            </el-upload>
          </div>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="难度等级">
              <el-select v-model="questionDialog.form.difficultyLevel" placeholder="请选择难度等级" style="width: 100%">
                <el-option label="简单" value="简单"/>
                <el-option label="中等" value="中等"/>
                <el-option label="困难" value="困难"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="题型">
              <el-select v-model="questionDialog.form.questionType" placeholder="请选择题型" style="width: 100%">
                <el-option label="选择题" value="选择题"/>
                <el-option label="填空题" value="填空题"/>
                <el-option label="计算题" value="计算题"/>
                <el-option label="证明题" value="证明题"/>
                <el-option label="解答题" value="解答题"/>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="分值">
              <el-input v-model="questionDialog.form.score" placeholder="请输入分值"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="年份">
              <el-input v-model="questionDialog.form.year" placeholder="请输入年份"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="关联知识点">
          <el-select
              v-model="questionDialog.form.knowledgePointIDs"
              multiple
              placeholder="请选择关联知识点"
              style="width: 100%"
          >
            <el-option
                v-for="knowledge in allKnowledgePoints"
                :key="knowledge.knowledgePointId"
                :label="knowledge.knowledgePointName"
                :value="knowledge.knowledgePointId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="questionDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveQuestion">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 试题知识点关联编辑对话框 -->
    <el-dialog
        v-model="relationDialog.visible"
        :title="relationDialog.isEdit ? '编辑关联' : '添加关联'"
        destroy-on-close
        width="50%"
    >
      <el-form :model="relationDialog.form" label-width="120px">
        <el-form-item label="试题">
          <el-select v-model="relationDialog.form.questionId" placeholder="请选择试题" style="width: 100%">
            <el-option
                v-for="question in formData.questionInputDTOList"
                :key="question.questionId"
                :label="`${question.questionId} - ${question.questionText.substring(0, 20)}...`"
                :value="question.questionId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="知识点">
          <el-select v-model="relationDialog.form.knowledgePointId" placeholder="请选择知识点" style="width: 100%">
            <el-option
                v-for="knowledge in allKnowledgePoints"
                :key="knowledge.knowledgePointId"
                :label="`${knowledge.knowledgePointId} - ${knowledge.knowledgePointName}`"
                :value="knowledge.knowledgePointId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="relationDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="saveRelation">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 子列表查看对话框 -->
    <el-dialog
        v-model="subItemsDialog.visible"
        :title="subItemsDialog.title"
        destroy-on-close
        width="60%"
    >
      <div v-if="subItemsDialog.parentDialogState" class="dialog-toolbar">
        <el-button size="small" type="primary" @click="subItemsDialog.handleBackToParent">
          <i class="el-icon-arrow-left"></i> 返回上级
        </el-button>
        <el-divider direction="vertical"></el-divider>
        <span class="breadcrumb-info">当前位置: {{ subItemsDialog.title }}</span>
      </div>

      <el-table :data="subItemsDialog.data" border style="width: 100%">
        <el-table-column
            v-for="col in subItemsDialog.columns"
            :key="col.prop"
            :label="col.label"
            :prop="col.prop"
            :width="col.width"
        />
        <!-- 添加操作列 -->
        <el-table-column label="操作" width="400">
          <template #default="scope">
            <el-button-group>
              <el-button size="small" type="primary" @click="editSubItem(scope.row, scope.$index)">编辑</el-button>
              <el-button size="small" type="success" @click="addSubItemChild(scope.row)">添加子项</el-button>
              <el-button size="small" type="info" @click="viewSubItemChildren(scope.row)">查看子项</el-button>
              <el-button size="small" type="danger" @click="deleteSubItem(scope.row, scope.$index)">删除</el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 添加空数据提示 -->
      <template v-if="subItemsDialog.data.length === 0">
        <div class="empty-data">
          <el-empty description="暂无数据"></el-empty>
        </div>
      </template>
    </el-dialog>

    <!-- 结果通知 -->
    <el-dialog
        v-model="resultDialog.visible"
        :title="resultDialog.success ? '操作成功' : '操作失败'"
        width="50%"
    >
      <div v-if="resultDialog.success" class="success-result">
        <el-result :title="resultDialog.title" icon="success"/>
      </div>
      <div v-else class="error-result">
        <el-result icon="error" title="操作失败">
          <template #sub-title>
            <div class="error-message">{{ resultDialog.message }}</div>
          </template>
        </el-result>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="resultDialog.visible = false">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {computed, onMounted, reactive, ref} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import request from '@/utils/request'

// 生成唯一ID
function generateId(prefix) {
  return `${prefix}_${Date.now()}`
}

// 获取所有章节（包括子章节）
function getAllChapters(chapters) {
  let allChapters = []

  function traverse(items) {
    if (!items) return

    items.forEach(item => {
      allChapters.push(item)
      if (item.childrenList && item.childrenList.length > 0) {
        traverse(item.childrenList)
      }
    })
  }

  traverse(chapters)
  return allChapters
}

// 获取所有知识点（包括子知识点）
function getAllKnowledgePoints(knowledgePoints) {
  let allPoints = []

  function traverse(items) {
    if (!items) return

    items.forEach(item => {
      allPoints.push(item)
      if (item.childrenList && item.childrenList.length > 0) {
        traverse(item.childrenList)
      }
    })
  }

  traverse(knowledgePoints)
  return allPoints
}

// 空数据结构，不提供默认值
const emptyFormData = {
  libraryId: "",
  libraryName: "",
  description: "",
  isLocal: false,
  isCloud: false,
  isPrivate: false,
  isPublic: false,
  createTime: "",
  userId: "",
  gradeInputDTO: {
    gradeId: "",
    gradeName: "",
    chapterInputDTOList: []
  },
  knowledgeInputDTOList: [],
  questionInputDTOList: [],
  knowledgeQuestionDTOList: []
}

export default {
  name: "QhWorkspace",
  setup() {
    // 设置折叠面板初始状态，只打开第一个
    const activeNames = ref(['basicInfo'])

    // 设置加载状态
    const loading = ref(false)

    // 上传类型
    const uploadType = ref('image')

    // 表单数据，使用空数据结构
    const formData = reactive(JSON.parse(JSON.stringify(emptyFormData)))

    // 表单验证状态
    const formValidation = reactive({
      basicInfoValid: false,
      gradeInfoValid: false,
      chapterInfoValid: false,
      knowledgeInfoValid: false
    })

    // 计算属性：各部分是否验证通过
    const isBasicInfoValid = computed(() => formValidation.basicInfoValid)
    const isGradeInfoValid = computed(() => formValidation.gradeInfoValid && formValidation.basicInfoValid)
    const isChapterInfoValid = computed(() => formValidation.chapterInfoValid && formValidation.gradeInfoValid && formValidation.basicInfoValid)
    const isKnowledgeInfoValid = computed(() => formValidation.knowledgeInfoValid && formValidation.chapterInfoValid && formValidation.gradeInfoValid && formValidation.basicInfoValid)

    // 验证题库基本信息
    const validateBasicInfo = () => {
      if (!formData.libraryId || !formData.libraryName || !formData.description || !formData.createTime || !formData.userId) {
        ElMessage.warning('请填写所有必填的题库基本信息')
        return
      }

      // 验证存储类型
      if (!formData.isLocal && !formData.isCloud) {
        ElMessage.warning('请至少选择一种存储类型')
        return
      }

      // 验证权限设置
      if (!formData.isPrivate && !formData.isPublic) {
        ElMessage.warning('请至少选择一种权限设置')
        return
      }

      // 设置为验证通过
      formValidation.basicInfoValid = true

      // 展开下一个面板
      activeNames.value = ['gradeInfo']
      ElMessage.success('题库基本信息验证通过，请填写年级信息')
    }

    // 返回题库信息
    const backToBasicInfo = () => {
      activeNames.value = ['basicInfo']
    }

    // 验证年级信息
    const validateGradeInfo = () => {
      if (!formData.gradeInputDTO.gradeId || !formData.gradeInputDTO.gradeName) {
        ElMessage.warning('请填写所有必填的年级信息')
        return
      }

      // 设置为验证通过
      formValidation.gradeInfoValid = true

      // 展开下一个面板
      activeNames.value = ['chapterInfo']
      ElMessage.success('年级信息验证通过，请添加章节信息')
    }

    // 返回年级信息
    const backToGradeInfo = () => {
      activeNames.value = ['gradeInfo']
    }

    // 验证章节信息
    const validateChapterInfo = () => {
      if (formData.gradeInputDTO.chapterInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个章节')
        return
      }

      // 检查章节是否填写完整
      for (const chapter of formData.gradeInputDTO.chapterInputDTOList) {
        if (!chapter.chapterId || !chapter.chapterName) {
          ElMessage.warning('存在章节信息不完整，请检查')
          return
        }
      }

      // 设置为验证通过
      formValidation.chapterInfoValid = true

      // 展开下一个面板
      activeNames.value = ['knowledgeInfo']
      ElMessage.success('章节信息验证通过，请添加知识点信息')
    }

    // 返回章节信息
    const backToChapterInfo = () => {
      activeNames.value = ['chapterInfo']
    }

    // 验证知识点信息
    const validateKnowledgeInfo = () => {
      if (formData.knowledgeInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个知识点')
        return
      }

      // 检查知识点是否填写完整
      for (const knowledge of formData.knowledgeInputDTOList) {
        if (!knowledge.knowledgePointId || !knowledge.knowledgePointName || !knowledge.chapterId) {
          ElMessage.warning('存在知识点信息不完整，请检查')
          return
        }
      }

      // 设置为验证通过
      formValidation.knowledgeInfoValid = true

      // 展开下一个面板
      activeNames.value = ['questionInfo']
      ElMessage.success('知识点信息验证通过，请添加试题信息')
    }

    // 返回知识点信息
    const backToKnowledgeInfo = () => {
      activeNames.value = ['knowledgeInfo']
    }

    // 章节对话框
    const chapterDialog = reactive({
      visible: false,
      isEdit: false,
      index: -1,
      parentId: null,
      parentName: '', // 添加父章节名称
      form: {
        chapterId: '',
        chapterName: '',
        parentChapterId: null,
        childrenList: []
      }
    })

    // 知识点对话框
    const knowledgeDialog = reactive({
      visible: false,
      isEdit: false,
      index: -1,
      parentId: null,
      parentName: '', // 添加父知识点名称
      form: {
        knowledgePointId: '',
        knowledgePointName: '',
        chapterId: '',
        parentKnowledgePointId: null,
        childrenList: []
      }
    })

    // 试题对话框
    const questionDialog = reactive({
      visible: false,
      isEdit: false,
      index: -1,
      form: {
        questionId: '',
        questionText: '',
        difficultyLevel: '',
        questionType: '',
        score: '',
        year: '',
        knowledgePointIDs: [],
        questionImage: '',
        answerImage: '',
        analysisImage: ''
      }
    })

    // 关联对话框
    const relationDialog = reactive({
      visible: false,
      isEdit: false,
      index: -1,
      form: {
        questionId: '',
        knowledgePointId: ''
      }
    })

    // 子项目查看对话框
    const subItemsDialog = reactive({
      visible: false,
      title: '',
      data: [],
      columns: [],
      type: '', // 添加类型标识：'chapter' 或 'knowledge'
      parentId: null, // 添加父项ID
      parentObj: null, // 添加父项对象引用
      parentDialogState: null, // 添加父对话框状态
      handleBackToParent: null // 添加返回函数
    })

    // 结果对话框
    const resultDialog = reactive({
      visible: false,
      success: false,
      title: '',
      message: ''
    })

    // 计算属性：获取所有章节（包括子章节）
    const allChapters = computed(() => {
      return getAllChapters(formData.gradeInputDTO.chapterInputDTOList)
    })

    // 计算属性：获取所有知识点（包括子知识点）
    const allKnowledgePoints = computed(() => {
      return getAllKnowledgePoints(formData.knowledgeInputDTOList)
    })

    // 根据ID查找章节
    const findChapterById = (chapters, id) => {
      if (!chapters || !id) return null

      for (let i = 0; i < chapters.length; i++) {
        if (chapters[i].chapterId === id) {
          return chapters[i]
        }

        if (chapters[i].childrenList && chapters[i].childrenList.length > 0) {
          const found = findChapterById(chapters[i].childrenList, id)
          if (found) return found
        }
      }

      return null
    }

    // 根据ID查找知识点
    const findKnowledgePointById = (knowledgePoints, id) => {
      if (!knowledgePoints || !id) return null

      for (let i = 0; i < knowledgePoints.length; i++) {
        if (knowledgePoints[i].knowledgePointId === id) {
          return knowledgePoints[i]
        }

        if (knowledgePoints[i].childrenList && knowledgePoints[i].childrenList.length > 0) {
          const found = findKnowledgePointById(knowledgePoints[i].childrenList, id)
          if (found) return found
        }
      }

      return null
    }

    // 添加章节
    const addChapter = (parentId) => {
      chapterDialog.isEdit = false
      chapterDialog.parentId = parentId

      // 获取父章节名称
      if (parentId) {
        const parentChapter = findChapterById(formData.gradeInputDTO.chapterInputDTOList, parentId)
        chapterDialog.parentName = parentChapter ? parentChapter.chapterName : ''
      } else {
        chapterDialog.parentName = ''
      }

      chapterDialog.form = {
        chapterId: generateId('7'),
        chapterName: '',
        parentChapterId: parentId ? parentId : "0",
        gradeId: formData.gradeInputDTO.gradeId,
        childrenList: []
      }
      chapterDialog.visible = true
    }

    // 编辑章节
    const editChapter = (row, index) => {
      chapterDialog.isEdit = true
      chapterDialog.index = index
      chapterDialog.form = JSON.parse(JSON.stringify(row))

      // 确保gradeId字段存在
      if (!chapterDialog.form.gradeId) {
        chapterDialog.form.gradeId = formData.gradeInputDTO.gradeId
      }

      // 确保父章节ID存在
      if (chapterDialog.form.parentChapterId === null || chapterDialog.form.parentChapterId === undefined) {
        chapterDialog.form.parentChapterId = "0"
      }

      chapterDialog.visible = true
    }

    // 保存章
    const saveChapter = () => {
      // 验证表单
      if (!chapterDialog.form.chapterName) {
        ElMessage.warning('请输入章节名称')
        return
      }

      if (chapterDialog.isEdit) {
        // 编辑现有章节
        const updateChapter = (chapters) => {
          for (let i = 0; i < chapters.length; i++) {
            if (chapters[i].chapterId === chapterDialog.form.chapterId) {
              // 保存子章节列表
              const childrenList = chapters[i].childrenList || []
              // 更新章节信息
              chapters[i] = {...chapterDialog.form}
              // 恢复子章节列表
              chapters[i].childrenList = childrenList
              return true
            }

            if (chapters[i].childrenList && chapters[i].childrenList.length > 0) {
              if (updateChapter(chapters[i].childrenList)) {
                return true
              }
            }
          }
          return false
        }

        // 递归查找并更新章节
        if (!updateChapter(formData.gradeInputDTO.chapterInputDTOList)) {
          // 如果在所有章节中未找到则更新顶层章节
          const chapters = formData.gradeInputDTO.chapterInputDTOList
          if (chapterDialog.index >= 0 && chapterDialog.index < chapters.length) {
            // 保存子章节列表
            const childrenList = chapters[chapterDialog.index].childrenList || []
            // 更新章节信息
            chapters[chapterDialog.index] = {...chapterDialog.form}
            // 恢复子章节列表
            chapters[chapterDialog.index].childrenList = childrenList
          }
        }
      } else {
        // 添加新章节
        if (chapterDialog.parentId) {
          // 添加子章节到父章节中
          const findAndAddChild = (chapters) => {
            for (let i = 0; i < chapters.length; i++) {
              if (chapters[i].chapterId === chapterDialog.parentId) {
                if (!chapters[i].childrenList) {
                  chapters[i].childrenList = []
                }
                // 确保新子章节有空的childrenList数组
                const newChapter = {...chapterDialog.form, childrenList: []}
                chapters[i].childrenList.push(newChapter)
                return true
              }

              if (chapters[i].childrenList && chapters[i].childrenList.length > 0) {
                if (findAndAddChild(chapters[i].childrenList)) {
                  return true
                }
              }
            }
            return false
          }

          findAndAddChild(formData.gradeInputDTO.chapterInputDTOList)
        } else {
          // 添加主章节，确保有空的childrenList
          formData.gradeInputDTO.chapterInputDTOList.push({
            ...chapterDialog.form,
            childrenList: []
          })
        }
      }

      chapterDialog.visible = false
      ElMessage.success(chapterDialog.isEdit ? '章节编辑成功' : '章节添加成功')
    }

    // 删除章节
    const deleteChapter = (row, index) => {
      // 确认是否删除
      ElMessageBox.confirm(
          `确定要删除章节 "${row.chapterName}" 吗？${row.childrenList && row.childrenList.length > 0 ? '该操作将同时删除所有子章节！' : ''}`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
      )
          .then(() => {
            if (!row.parentChapterId || row.parentChapterId === "0") {
              // 如果是主章节，直接从列表中删除
              formData.gradeInputDTO.chapterInputDTOList.splice(index, 1)
              ElMessage.success('章节删除成功')
            } else {
              // 如果是子章节，需要从父章节的childrenList中删除
              const removeFromParent = (chapters, parentId, chapterId) => {
                for (let i = 0; i < chapters.length; i++) {
                  if (chapters[i].chapterId === parentId) {
                    if (chapters[i].childrenList) {
                      const childIndex = chapters[i].childrenList.findIndex(c => c.chapterId === chapterId)
                      if (childIndex !== -1) {
                        chapters[i].childrenList.splice(childIndex, 1)
                        return true
                      }
                    }
                  }

                  if (chapters[i].childrenList && chapters[i].childrenList.length > 0) {
                    if (removeFromParent(chapters[i].childrenList, parentId, chapterId)) {
                      return true
                    }
                  }
                }
                return false
              }

              if (removeFromParent(formData.gradeInputDTO.chapterInputDTOList, row.parentChapterId, row.chapterId)) {
                ElMessage.success('章节删除成功')
              } else {
                ElMessage.error('未找到指定的章节')
              }
            }
          })
          .catch(() => {
            // 用户取消删除
            ElMessage.info('已取消删除')
          })
    }

    // 查看子章节
    const viewSubChapters = (row) => {
      // 子章节数据不为空才展示
      if (!row.childrenList || row.childrenList.length === 0) {
        ElMessage.info(`${row.chapterName} 没有子章节`)
        return
      }

      // 关闭之前可能打开的子项对话框，避免信息重叠
      subItemsDialog.visible = false

      // 延迟执行，确保之前的对话框已关闭
      setTimeout(() => {
        // 重置对话框状态
        subItemsDialog.parentDialogState = null
        subItemsDialog.handleBackToParent = null

        subItemsDialog.title = `${row.chapterName} 的子章节列表`
        subItemsDialog.data = JSON.parse(JSON.stringify(row.childrenList)) || []
        subItemsDialog.type = 'chapter' // 设置类型为章节
        subItemsDialog.parentId = row.chapterId // 设置父章节ID
        subItemsDialog.parentObj = row // 保存父对象引用

        // 为子章节数据添加gradeId字段
        subItemsDialog.data.forEach(item => {
          item.gradeId = formData.gradeInputDTO.gradeId
        })

        subItemsDialog.columns = [
          {label: '年级ID', prop: 'gradeId', width: '180'},
          {label: '章节ID', prop: 'chapterId', width: '180'},
          {label: '章节名称', prop: 'chapterName', width: '180'},
          {label: '父章节ID', prop: 'parentChapterId', width: '180'}
        ]

        subItemsDialog.visible = true
      }, 100)
    }

    // 添加知识点
    const addKnowledge = (parentId) => {
      knowledgeDialog.isEdit = false
      knowledgeDialog.parentId = parentId

      // 获取父知识点名称
      if (parentId) {
        const parentKnowledge = findKnowledgePointById(formData.knowledgeInputDTOList, parentId)
        knowledgeDialog.parentName = parentKnowledge ? parentKnowledge.knowledgePointName : ''
      } else {
        knowledgeDialog.parentName = ''
      }

      knowledgeDialog.form = {
        knowledgePointId: generateId('6'),
        knowledgePointName: '',
        chapterId: '',
        parentKnowledgePointId: parentId ? parentId : "0",
        childrenList: []
      }
      knowledgeDialog.visible = true
    }

    // 编辑知识点
    const editKnowledge = (row, index) => {
      knowledgeDialog.isEdit = true
      knowledgeDialog.index = index
      knowledgeDialog.form = JSON.parse(JSON.stringify(row))

      // 确保父知识点ID存在
      if (knowledgeDialog.form.parentKnowledgePointId === null || knowledgeDialog.form.parentKnowledgePointId === undefined) {
        knowledgeDialog.form.parentKnowledgePointId = "0"
      }

      knowledgeDialog.visible = true
    }

    // 保存知识点
    const saveKnowledge = () => {
      // 验证表单
      if (!knowledgeDialog.form.knowledgePointName) {
        ElMessage.warning('请输入知识点名称')
        return
      }

      if (!knowledgeDialog.form.chapterId) {
        ElMessage.warning('请选择所属章节')
        return
      }

      if (knowledgeDialog.isEdit) {
        // 编辑现有知识点
        const updateKnowledge = (knowledgePoints) => {
          for (let i = 0; i < knowledgePoints.length; i++) {
            if (knowledgePoints[i].knowledgePointId === knowledgeDialog.form.knowledgePointId) {
              // 保存子知识点列表
              const childrenList = knowledgePoints[i].childrenList || []
              // 更新知识点信息
              knowledgePoints[i] = {...knowledgeDialog.form}
              // 恢复子知识点列表
              knowledgePoints[i].childrenList = childrenList
              return true
            }

            if (knowledgePoints[i].childrenList && knowledgePoints[i].childrenList.length > 0) {
              if (updateKnowledge(knowledgePoints[i].childrenList)) {
                return true
              }
            }
          }
          return false
        }

        // 递归查找并更新知识点
        if (!updateKnowledge(formData.knowledgeInputDTOList)) {
          // 如果在所有知识点中未找到则更新顶层知识点
          const knowledgePoints = formData.knowledgeInputDTOList
          if (knowledgeDialog.index >= 0 && knowledgeDialog.index < knowledgePoints.length) {
            // 保存子知识点列表
            const childrenList = knowledgePoints[knowledgeDialog.index].childrenList || []
            // 更新知识点信息
            knowledgePoints[knowledgeDialog.index] = {...knowledgeDialog.form}
            // 恢复子知识点列表
            knowledgePoints[knowledgeDialog.index].childrenList = childrenList
          }
        }
      } else {
        // 添加新知识点
        if (knowledgeDialog.parentId) {
          // 添加子知识点到父知识点中
          const findAndAddChild = (knowledgePoints) => {
            for (let i = 0; i < knowledgePoints.length; i++) {
              if (knowledgePoints[i].knowledgePointId === knowledgeDialog.parentId) {
                if (!knowledgePoints[i].childrenList) {
                  knowledgePoints[i].childrenList = []
                }
                // 确保新子知识点有空的childrenList数组
                const newKnowledge = {...knowledgeDialog.form, childrenList: []}
                knowledgePoints[i].childrenList.push(newKnowledge)
                return true
              }

              if (knowledgePoints[i].childrenList && knowledgePoints[i].childrenList.length > 0) {
                if (findAndAddChild(knowledgePoints[i].childrenList)) {
                  return true
                }
              }
            }
            return false
          }

          findAndAddChild(formData.knowledgeInputDTOList)
        } else {
          // 添加主知识点，确保有空的childrenList
          formData.knowledgeInputDTOList.push({
            ...knowledgeDialog.form,
            childrenList: []
          })
        }
      }

      knowledgeDialog.visible = false
      ElMessage.success(knowledgeDialog.isEdit ? '知识点编辑成功' : '知识点添加成功')
    }

    // 删除知识点
    const deleteKnowledge = (row, index) => {
      // 确认是否删除
      ElMessageBox.confirm(
          `确定要删除知识点 "${row.knowledgePointName}" 吗？${row.childrenList && row.childrenList.length > 0 ? '该操作将同时删除所有子知识点！' : ''}`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
      )
          .then(() => {
            if (!row.parentKnowledgePointId || row.parentKnowledgePointId === "0") {
              // 如果是主知识点，直接从列表中删除
              formData.knowledgeInputDTOList.splice(index, 1)
              ElMessage.success('知识点删除成功')
            } else {
              // 如果是子知识点，需要从父知识点的childrenList中删除
              const removeFromParent = (knowledgePoints, parentId, knowledgePointId) => {
                for (let i = 0; i < knowledgePoints.length; i++) {
                  if (knowledgePoints[i].knowledgePointId === parentId) {
                    if (knowledgePoints[i].childrenList) {
                      const childIndex = knowledgePoints[i].childrenList.findIndex(c => c.knowledgePointId === knowledgePointId)
                      if (childIndex !== -1) {
                        knowledgePoints[i].childrenList.splice(childIndex, 1)
                        return true
                      }
                    }
                  }

                  if (knowledgePoints[i].childrenList && knowledgePoints[i].childrenList.length > 0) {
                    if (removeFromParent(knowledgePoints[i].childrenList, parentId, knowledgePointId)) {
                      return true
                    }
                  }
                }
                return false
              }

              if (removeFromParent(formData.knowledgeInputDTOList, row.parentKnowledgePointId, row.knowledgePointId)) {
                ElMessage.success('知识点删除成功')
              } else {
                ElMessage.error('未找到指定的知识点')
              }
            }
          })
          .catch(() => {
            // 用户取消删除
            ElMessage.info('已取消删除')
          })
    }

    // 查看子知识点
    const viewSubKnowledge = (row) => {
      // 子知识点数据不为空才展示
      if (!row.childrenList || row.childrenList.length === 0) {
        ElMessage.info(`${row.knowledgePointName} 没有子知识点`)
        return
      }

      // 关闭之前可能打开的子项对话框，避免信息重叠
      subItemsDialog.visible = false

      // 延迟执行，确保之前的对话框已关闭
      setTimeout(() => {
        // 重置对话框状态
        subItemsDialog.parentDialogState = null
        subItemsDialog.handleBackToParent = null

        subItemsDialog.title = `${row.knowledgePointName} 的子知识点列表`
        subItemsDialog.data = JSON.parse(JSON.stringify(row.childrenList)) || []
        subItemsDialog.type = 'knowledge' // 设置类型为知识点
        subItemsDialog.parentId = row.knowledgePointId // 设置父知识点ID
        subItemsDialog.parentObj = row // 保存父对象引用

        subItemsDialog.columns = [
          {label: '知识点ID', prop: 'knowledgePointId', width: '180'},
          {label: '知识点名称', prop: 'knowledgePointName', width: '180'},
          {label: '所属章节ID', prop: 'chapterId', width: '180'},
          {label: '父知识点ID', prop: 'parentKnowledgePointId', width: '180'}
        ]

        subItemsDialog.visible = true
      }, 100)
    }

    // 添加试题
    const addQuestion = () => {
      questionDialog.isEdit = false
      questionDialog.form = {
        questionId: generateId('8'),
        questionText: '',
        difficultyLevel: '',
        questionType: '',
        score: '',
        year: '',
        knowledgePointIDs: [],
        questionImage: '',
        answerImage: '',
        analysisImage: ''
      }
      questionDialog.visible = true
    }

    // 编辑试题
    const editQuestion = (row, index) => {
      questionDialog.isEdit = true
      questionDialog.index = index
      questionDialog.form = JSON.parse(JSON.stringify(row))
      questionDialog.visible = true
    }

    // 保存试题
    const saveQuestion = () => {
      // 验证表单
      if (!questionDialog.form.questionText) {
        ElMessage.warning('请输入试题内容')
        return
      }

      if (!questionDialog.form.difficultyLevel) {
        ElMessage.warning('请选择难度等级')
        return
      }

      if (!questionDialog.form.questionType) {
        ElMessage.warning('请选择题型')
        return
      }

      if (questionDialog.isEdit) {
        // 编辑现有试题
        formData.questionInputDTOList[questionDialog.index] = {...questionDialog.form}

        // 同步更新关联表中的试题ID
        if (questionDialog.form.questionId !== formData.questionInputDTOList[questionDialog.index].questionId) {
          if (formData.knowledgeQuestionDTOList) {
            formData.knowledgeQuestionDTOList.forEach(relation => {
              if (relation.questionId === formData.questionInputDTOList[questionDialog.index].questionId) {
                relation.questionId = questionDialog.form.questionId
              }
            })
          }
        }
      } else {
        // 添加新试题
        formData.questionInputDTOList.push(questionDialog.form)

        // 自动添加知识点关联
        if (questionDialog.form.knowledgePointIDs && questionDialog.form.knowledgePointIDs.length > 0) {
          // 确保knowledgeQuestionDTOList数组存在
          if (!formData.knowledgeQuestionDTOList) {
            formData.knowledgeQuestionDTOList = []
          }

          questionDialog.form.knowledgePointIDs.forEach(knowledgePointId => {
            formData.knowledgeQuestionDTOList.push({
              questionId: questionDialog.form.questionId,
              knowledgePointId: knowledgePointId
            })
          })
        }
      }

      questionDialog.visible = false
      ElMessage.success(questionDialog.isEdit ? '试题编辑成功' : '试题添加成功')
    }

    // 删除试题
    const deleteQuestion = (row, index) => {
      const questionId = row.questionId || formData.questionInputDTOList[index].questionId

      // 删除试题
      formData.questionInputDTOList.splice(index, 1)

      // 删除关联的知识点关联
      if (formData.knowledgeQuestionDTOList) {
        const newRelations = formData.knowledgeQuestionDTOList.filter(relation => relation.questionId !== questionId)
        formData.knowledgeQuestionDTOList = newRelations
      }

      ElMessage.success('试题删除成功')
    }

    // 添加试题知识点关联
    const addQuestionKnowledgeRelation = (questionId) => {
      relationDialog.isEdit = false
      relationDialog.form = {
        questionId: questionId || '',
        knowledgePointId: ''
      }
      relationDialog.visible = true
    }

    // 编辑试题知识点关联
    const editRelation = (row, index) => {
      relationDialog.isEdit = true
      relationDialog.index = index
      relationDialog.form = {...row}
      relationDialog.visible = true
    }

    // 保存试题知识点关联
    const saveRelation = () => {
      // 验证表单
      if (!relationDialog.form.questionId || !relationDialog.form.knowledgePointId) {
        ElMessage.warning('请选择试题和知识点')
        return
      }

      if (relationDialog.isEdit) {
        // 编辑现有关联
        if (formData.knowledgeQuestionDTOList && formData.knowledgeQuestionDTOList[relationDialog.index]) {
          formData.knowledgeQuestionDTOList[relationDialog.index] = {...relationDialog.form}
        }
      } else {
        // 添加新关联
        if (!formData.knowledgeQuestionDTOList) {
          formData.knowledgeQuestionDTOList = []
        }
        formData.knowledgeQuestionDTOList.push({...relationDialog.form})
      }

      relationDialog.visible = false
      ElMessage.success(relationDialog.isEdit ? '关联编辑成功' : '关联添加成功')
    }

    // 删除试题知识点关联
    const deleteRelation = (index) => {
      const relation = formData.knowledgeQuestionDTOList[index]

      // 删除关联
      formData.knowledgeQuestionDTOList.splice(index, 1)

      // 更新试题的知识点ID数组
      const questionIndex = formData.questionInputDTOList.findIndex(q => q.questionId === relation.questionId)
      if (questionIndex !== -1 && formData.questionInputDTOList[questionIndex].knowledgePointIDs) {
        const kpIdIndex = formData.questionInputDTOList[questionIndex].knowledgePointIDs.indexOf(relation.knowledgePointId)
        if (kpIdIndex !== -1) {
          formData.questionInputDTOList[questionIndex].knowledgePointIDs.splice(kpIdIndex, 1)
        }
      }

      ElMessage.success('关联删除成功')
    }

    // 添加图片上传前处理函数
    const beforeImageUpload = (file, field) => {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isJPG && !isPNG) {
        ElMessage.error('图片只能是JPG或PNG格式!')
        return false
      }

      if (!isLt2M) {
        ElMessage.error('图片大小不能超过2MB!')
        return false
      }

      // 使用FileReader将文件转换为base64
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        questionDialog.form[field] = reader.result
      }

      return false // 阻止默认上传，我们手动处理
    }

    // 优化图片URL转base64的函数，添加对第二个题目的特殊处理
    const convertImageUrlToBase64 = async (url, questionId, imageType) => {
      // 添加调试信息
      console.log(`开始转换图片，题目ID: ${questionId}, 图片类型: ${imageType}, URL: ${url?.substring(0, 50) || '无'}`);

      if (!url) {
        console.log(`${questionId}的${imageType}图片URL为空，跳过转换`);
        return '';
      }

      // 如果已经是base64格式，直接返回
      if (url.startsWith('data:image/')) {
        console.log(`${questionId}的${imageType}图片已经是base64格式，跳过转换`);
        return url;
      }

      // 对于picui.cn的图片使用特殊处理
      if (url.includes('picui.cn')) {
        try {
          // 对于picui.cn域名的图片，使用Image和Canvas方式处理
          console.log(`${questionId}的${imageType}图片是picui.cn域名，使用Canvas方式处理`);
          return await convertImageUsingCanvas(url, questionId, imageType);
        } catch (error) {
          console.error(`picui.cn图片处理失败:`, error);

          // 尝试使用硬编码的默认图片替代
          console.log(`尝试使用默认图片替代`);
          // 使用一个简单的1x1像素透明PNG的base64编码
          const transparentPixel = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
          return transparentPixel;
        }
      }

      try {
        console.log(`使用fetch方法获取${questionId}的${imageType}图片: ${url.substring(0, 30)}...`);

        // 添加超时处理
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

        // 使用fetch获取图片
        const response = await fetch(url, {
          signal: controller.signal,
          mode: 'cors', // 尝试跨域请求
          headers: {
            'Accept': 'image/*',
          }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`图片请求失败: ${response.status} ${response.statusText}`);
        }

        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('image/')) {
          throw new Error(`返回的内容不是图片: ${contentType}`);
        }

        const blob = await response.blob();
        console.log(`${questionId}的${imageType}图片成功获取为blob，大小: ${blob.size} 字节`);

        // 使用FileReader将blob转为base64
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            console.log(`${questionId}的${imageType}图片成功转换为base64，长度: ${reader.result.length}`);
            resolve(reader.result);
          };
          reader.onerror = (error) => {
            console.error(`FileReader读取失败:`, error);
            reject(error);
          };
          reader.readAsDataURL(blob);
        });
      } catch (error) {
        console.error(`转换${questionId}的${imageType}图片失败:`, error);

        // 为所有图片都尝试使用Canvas方法
        try {
          console.log(`尝试使用Canvas方法转换${questionId}的${imageType}图片`);
          return await convertImageUsingCanvas(url, questionId, imageType);
        } catch (backupError) {
          console.error(`Canvas方法转换失败:`, backupError);

          // 使用一个简单的1x1像素透明PNG的base64编码作为默认图片
          const transparentPixel = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
          console.log(`使用默认透明图片替代`);
          return transparentPixel;
        }
      }
    };

    // 改进Canvas方式转换图片
    const convertImageUsingCanvas = (url, questionId, imageType) => {
      return new Promise((resolve, reject) => {
        console.log(`Canvas方法开始处理 ${questionId} 的 ${imageType} 图片`);

        const img = new Image();
        img.crossOrigin = 'Anonymous'; // 尝试解决跨域问题

        img.onload = () => {
          try {
            console.log(`${questionId}的${imageType}图片已加载，尺寸: ${img.width}x${img.height}`);

            // 创建Canvas并绘制图片
            const canvas = document.createElement('canvas');
            canvas.width = img.width || 100;  // 使用默认宽度，防止宽度为0
            canvas.height = img.height || 100; // 使用默认高度，防止高度为0

            const ctx = canvas.getContext('2d');
            ctx.fillStyle = "#FFFFFF"; // 填充白色背景
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);

            // 转换为base64
            const dataURL = canvas.toDataURL('image/png');
            console.log(`${questionId}的${imageType}图片已通过Canvas成功转换为base64，长度: ${dataURL.length}`);
            resolve(dataURL);
          } catch (error) {
            console.error(`Canvas绘制失败:`, error);
            reject(error);
          }
        };

        img.onerror = (error) => {
          console.error(`图片加载失败:`, error);

          // 尝试添加跨域头后重试
          console.log(`尝试添加referrer策略后重试`);
          img.referrerPolicy = 'no-referrer';

          // 设置重试逻辑
          img.onerror = (retryError) => {
            console.error(`重试加载失败:`, retryError);
            reject(retryError);
          };

          // 添加时间戳以避免缓存并重试
          img.src = `${url}${url.includes('?') ? '&' : '?'}t=${Date.now()}&retry=1`;
        };

        // 添加时间戳以避免缓存
        img.src = `${url}${url.includes('?') ? '&' : '?'}t=${Date.now()}`;

        // 设置超时
        setTimeout(() => {
          if (!img.complete) {
            console.error(`图片加载超时: ${url}`);
            img.src = ''; // 取消加载
            reject(new Error(`图片加载超时: ${url}`));
          }
        }, 10000);
      });
    };

    // 使用本地数据URI替代远程图片
    const getLocalImageBase64 = (questionId, imageType) => {
      // 使用一个简单的彩色方块图作为base64编码的默认图片
      // 这是一个20x20像素的简单彩色图片
      const colorBoxBase64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAKElEQVQ4jWNgYGD4Twz+DxBgGjWQBIpGE/k/aOA/MlesUQOHp4EjDwAA8J5BEQRdGC0AAAAASUVORK5CYII=';

      // 对于所有题目，包括第三个及以后的题目，都返回相同的base64图片
      // 在实际应用中，可以根据questionId或imageType返回不同的图片
      return colorBoxBase64;
    };

    // 提交表单
    const submitForm = () => {
      // 表单验证
      if (!formData.libraryId || !formData.libraryName) {
        ElMessage.warning('请完善题库基本信息')
        return
      }

      if (!formData.gradeInputDTO.gradeId || !formData.gradeInputDTO.gradeName) {
        ElMessage.warning('请完善年级信息')
        return
      }

      if (formData.gradeInputDTO.chapterInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个章节')
        return
      }

      if (formData.knowledgeInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个知识点')
        return
      }

      if (formData.questionInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个试题')
        return
      }

      // 确认提交
      ElMessageBox.confirm('确认提交所有数据吗？', '提交确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            loading.value = true

            // 模拟API请求
            setTimeout(() => {
              loading.value = false
              resultDialog.success = true
              resultDialog.message = '数据入库成功！'
              resultDialog.visible = true
            }, 1500)
          })
          .catch(() => {
            ElMessage.info('已取消提交')
          })
    }

    // 重置表单
    const resetForm = () => {
      // 弹出确认对话框
      ElMessageBox.confirm('确定要重置所有数据吗？此操作不可恢复！', '重置确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            // 重置为空数据
            Object.assign(formData, JSON.parse(JSON.stringify(emptyFormData)))

            // 重置验证状态
            Object.assign(formValidation, {
              basicInfoValid: false,
              gradeInfoValid: false,
              chapterInfoValid: false,
              knowledgeInfoValid: false
            })

            // 重置面板状态，只显示第一个
            activeNames.value = ['basicInfo']

            // 清除本地存储的数据
            localStorage.removeItem('qh_workspace_data')

            ElMessage.success('表单已重置，请重新填写')
          })
          .catch(() => {
            ElMessage.info('已取消重置')
          })
    }

    // 表格行样式 - 章节表格
    const chapterRowClassName = ({row}) => {
      if (row.childrenList && row.childrenList.length > 0) {
        return 'has-children'
      } else if (row.parentChapterId && row.parentChapterId !== '0') {
        return 'is-child'
      }
      return ''
    }

    // 表格行样式 - 知识点表格
    const knowledgeRowClassName = ({row}) => {
      if (row.childrenList && row.childrenList.length > 0) {
        return 'has-children'
      } else if (row.parentKnowledgePointId && row.parentKnowledgePointId !== '0') {
        return 'is-child'
      }
      return ''
    }

    // 编辑子项
    const editSubItem = (row, index) => {
      if (subItemsDialog.type === 'chapter') {
        // 编辑子章节
        editChapter(row, index)
      } else if (subItemsDialog.type === 'knowledge') {
        // 编辑子知识点
        editKnowledge(row, index)
      }
    }

    // 添加子项的子项
    const addSubItemChild = (row) => {
      if (subItemsDialog.type === 'chapter') {
        // 添加子章节的子章节
        addChapter(row.chapterId)
      } else if (subItemsDialog.type === 'knowledge') {
        // 添加子知识点的子知识点
        addKnowledge(row.knowledgePointId)
      }
    }

    // 查看子项的子项
    const viewSubItemChildren = (row) => {
      if (subItemsDialog.type === 'chapter') {
        // 查看子章节的子章节
        if (row.childrenList && row.childrenList.length > 0) {
          const parentDialogState = {
            title: subItemsDialog.title,
            data: [...subItemsDialog.data],
            type: subItemsDialog.type,
            parentId: subItemsDialog.parentId,
            parentObj: subItemsDialog.parentObj
          }

          // 更新对话框数据
          subItemsDialog.title = `${row.chapterName} 的子章节列表`
          subItemsDialog.data = [...row.childrenList]
          subItemsDialog.parentObj = row
          subItemsDialog.parentDialogState = parentDialogState
        } else {
          ElMessage.info(`${row.chapterName} 没有子章节`)
        }
      } else if (subItemsDialog.type === 'knowledge') {
        // 查看子知识点的子知识点
        if (row.childrenList && row.childrenList.length > 0) {
          const parentDialogState = {
            title: subItemsDialog.title,
            data: [...subItemsDialog.data],
            type: subItemsDialog.type,
            parentId: subItemsDialog.parentId,
            parentObj: subItemsDialog.parentObj
          }

          // 更新对话框数据
          subItemsDialog.title = `${row.knowledgePointName} 的子知识点列表`
          subItemsDialog.data = [...row.childrenList]
          subItemsDialog.parentObj = row
          subItemsDialog.parentDialogState = parentDialogState
        } else {
          ElMessage.info(`${row.knowledgePointName} 没有子知识点`)
        }
      }
    }

    // 删除子项
    const deleteSubItem = (row, index) => {
      // 确认是否删除
      const entityType = subItemsDialog.type === 'chapter' ? '章节' : '知识点'
      const entityName = subItemsDialog.type === 'chapter' ? row.chapterName : row.knowledgePointName

      ElMessageBox.confirm(
          `确定要删除${entityType} "${entityName}" 吗？${row.childrenList && row.childrenList.length > 0 ? `该操作将同时删除所有子${entityType}！` : ''}`,
          '删除确认',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
      )
          .then(() => {
            if (subItemsDialog.type === 'chapter') {
              // 从子项列表中删除章节
              subItemsDialog.data.splice(index, 1)
              if (subItemsDialog.parentObj && subItemsDialog.parentObj.childrenList) {
                // 也从父对象的子列表中删除
                const childIndex = subItemsDialog.parentObj.childrenList.findIndex(c => c.chapterId === row.chapterId)
                if (childIndex !== -1) {
                  subItemsDialog.parentObj.childrenList.splice(childIndex, 1)
                }
              }
              ElMessage.success('章节删除成功')
            } else if (subItemsDialog.type === 'knowledge') {
              // 从子项列表中删除知识点
              subItemsDialog.data.splice(index, 1)
              if (subItemsDialog.parentObj && subItemsDialog.parentObj.childrenList) {
                // 也从父对象的子列表中删除
                const childIndex = subItemsDialog.parentObj.childrenList.findIndex(c => c.knowledgePointId === row.knowledgePointId)
                if (childIndex !== -1) {
                  subItemsDialog.parentObj.childrenList.splice(childIndex, 1)
                }
              }
              ElMessage.success('知识点删除成功')
            }
          })
          .catch(() => {
            ElMessage.info('已取消删除')
          })
    }

    // 在组件挂载时加载本地存储的数据
    onMounted(() => {
      loadSavedData();
    });

    // 从本地存储加载数据
    const loadSavedData = () => {
      try {
        const savedData = localStorage.getItem('qh_workspace_data');
        if (savedData) {
          const parsedData = JSON.parse(savedData);
          // 将保存的数据合并到 formData 中
          Object.assign(formData, parsedData);

          // 如果有保存的数据，则设置相应的验证状态
          if (formData.libraryId && formData.libraryName && formData.description) {
            formValidation.basicInfoValid = true;
          }

          if (formData.gradeInputDTO && formData.gradeInputDTO.gradeId && formData.gradeInputDTO.gradeName) {
            formValidation.gradeInfoValid = true;
          }

          if (formData.gradeInputDTO && formData.gradeInputDTO.chapterInputDTOList && formData.gradeInputDTO.chapterInputDTOList.length > 0) {
            formValidation.chapterInfoValid = true;
          }

          if (formData.knowledgeInputDTOList && formData.knowledgeInputDTOList.length > 0) {
            formValidation.knowledgeInfoValid = true;
          }

          ElMessage.success('已从本地存储加载数据');
        }
      } catch (error) {
        console.error('加载保存的数据时出错:', error);
        ElMessage.error('加载保存的数据时出错');
      }
    };

    // 将数据保存到本地存储
    const saveFormData = () => {
      // 表单验证
      if (!formData.libraryId || !formData.libraryName) {
        ElMessage.warning('请完善题库基本信息')
        return
      }

      if (!formData.gradeInputDTO.gradeId || !formData.gradeInputDTO.gradeName) {
        ElMessage.warning('请完善年级信息')
        return
      }

      if (formData.gradeInputDTO.chapterInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个章节')
        return
      }

      if (formData.knowledgeInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个知识点')
        return
      }

      if (formData.questionInputDTOList.length === 0) {
        ElMessage.warning('请至少添加一个试题')
        return
      }

      try {
        // 将数据保存到本地存储
        localStorage.setItem('qh_workspace_data', JSON.stringify(formData));

        resultDialog.success = true;
        resultDialog.title = '数据保存成功！';
        resultDialog.message = '';
        resultDialog.visible = true;
      } catch (error) {
        console.error('保存数据时出错:', error);
        resultDialog.success = false;
        resultDialog.title = '';
        resultDialog.message = `保存数据时出错: ${error.message}`;
        resultDialog.visible = true;
      }
    }

    // 题库入库 - 调用后端接口
    const inStorage = () => {
      // 检查是否已保存数据
      const savedData = localStorage.getItem('qh_workspace_data');
      if (!savedData) {
        ElMessage.warning('请先保存数据再进行入库操作');
        return;
      }

      // 确认提交
      ElMessageBox.confirm('确认将数据提交到后端入库吗？', '入库确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
          .then(() => {
            loading.value = true;

            // 准备请求数据
            const requestData = JSON.parse(JSON.stringify(formData));

            // 将数据外层套一层数组
            const wrappedData = [requestData];

            // 调用后端接口
            request({
              url: '/qh/external-upload/inStorage',
              method: 'post',
              data: wrappedData
            })
                .then(response => {
                  loading.value = false;

                  // 处理响应
                  if (response && response.code === 200) {
                    resultDialog.success = true;
                    resultDialog.title = '题库入库成功！';
                    resultDialog.message = '';
                  } else {
                    resultDialog.success = false;
                    resultDialog.title = '';
                    resultDialog.message = response?.msg || '入库失败，请检查数据格式';
                  }
                  resultDialog.visible = true;
                })
                .catch(error => {
                  loading.value = false;
                  console.error('题库入库请求失败:', error);

                  resultDialog.success = false;
                  resultDialog.title = '';
                  resultDialog.message = error.message || '接口调用失败，请稍后再试';
                  resultDialog.visible = true;
                });
          })
          .catch(() => {
            ElMessage.info('已取消入库操作');
          });
    }

    return {
      activeNames,
      loading,
      uploadType,
      formData,
      allChapters,
      allKnowledgePoints,
      chapterDialog,
      knowledgeDialog,
      questionDialog,
      relationDialog,
      subItemsDialog,
      resultDialog,
      chapterRowClassName,
      knowledgeRowClassName,
      addChapter,
      editChapter,
      saveChapter,
      deleteChapter,
      viewSubChapters,
      addKnowledge,
      editKnowledge,
      saveKnowledge,
      deleteKnowledge,
      viewSubKnowledge,
      addQuestion,
      editQuestion,
      saveQuestion,
      deleteQuestion,
      addQuestionKnowledgeRelation,
      editRelation,
      saveRelation,
      deleteRelation,
      beforeImageUpload,
      submitForm,
      resetForm,
      // 添加新函数到返回对象
      editSubItem,
      addSubItemChild,
      viewSubItemChildren,
      deleteSubItem,
      // 添加表单验证和导航函数
      isBasicInfoValid,
      isGradeInfoValid,
      isChapterInfoValid,
      isKnowledgeInfoValid,
      validateBasicInfo,
      validateGradeInfo,
      validateChapterInfo,
      validateKnowledgeInfo,
      backToBasicInfo,
      backToGradeInfo,
      backToChapterInfo,
      backToKnowledgeInfo,
      saveFormData,
      inStorage
    }
  }
}
</script>

<style>
/* 全局样式，用于强制修改Element Plus组件样式 */
.qh-workspace-container .el-table {
  font-size: 15px !important; /* 增大表格字体 */
  width: 100% !important; /* 表格宽度100% */
}

.qh-workspace-container .el-table__inner-wrapper {
  width: 100% !important; /* 确保内部包装也是100%宽度 */
}

.qh-workspace-container .el-table th {
  font-size: 16px !important; /* 增大表头字体 */
  background-color: #F5F7FA !important;
  color: #303133 !important;
  font-weight: bold !important;
}

.qh-workspace-container .el-form-item__label {
  font-size: 15px !important; /* 增大表单标签字体 */
}

.qh-workspace-container .el-input__inner {
  font-size: 15px !important; /* 增大输入框字体 */
}

.qh-workspace-container .el-button {
  font-size: 14px !important; /* 增大按钮字体 */
}

/* 表格操作按钮横向紧凑排列 */
.qh-workspace-container .el-button-group {
  display: flex !important;
}

/* 标题栏样式 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #303133;
  font-size: 20px;
  font-weight: bold;
}

.subtitle {
  font-size: 16px; /* 增大副标题字体 */
  color: #909399;
  margin-left: 10px;
}

.action-bar {
  margin-bottom: 15px;
}

.form-actions {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.step-info {
  margin-bottom: 15px;
}

/* 步骤提示样式 */
.el-alert {
  margin-bottom: 15px;
}

/* 表单项必填标记样式 */
.el-form-item.is-required .el-form-item__label:before {
  color: #F56C6C;
}

/* 已验证通过的面板标题 */
.el-collapse-item.is-validated .el-collapse-item__header {
  color: #67C23A;
}

/* 未激活的面板标题 */
.el-collapse-item.is-disabled .el-collapse-item__header {
  color: #909399;
  cursor: not-allowed;
}

/* 当前激活的面板标题 */
.el-collapse-item.is-active .el-collapse-item__header {
  font-weight: bold;
  color: #409EFF;
}

/* 父节点样式 */
.has-children {
  font-weight: bold;
  background-color: rgba(64, 158, 255, 0.1);
}

/* 子节点样式 */
.is-child {
  background-color: rgba(103, 194, 58, 0.1);
  padding-left: 20px;
}

/* 分隔线上的操作按钮组 */
.action-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
}

.action-item {
  display: flex;
  align-items: center;
}

.action-label {
  margin-right: 10px;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

/* 父-子关系信息显示 */
.parent-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  font-weight: bold;
  margin-right: 5px;
}

/* 面包屑导航 */
.breadcrumb-container {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  background-color: #f5f7fa;
  padding: 8px 15px;
  border-radius: 4px;
}

.breadcrumb-info {
  color: #606266;
  font-size: 14px;
  margin-left: 10px;
}

.empty-data {
  margin: 20px 0;
  text-align: center;
}
</style>
